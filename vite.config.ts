/// <reference types="vitest" />
/// <reference types="vite/client" />
import { defineConfig } from 'vite'
import { reactRouter } from "@react-router/dev/vite";
import { fileURLToPath } from 'url'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  resolve: {
    alias: {
      '~': fileURLToPath(new URL('./src', import.meta.url)),
      'react': fileURLToPath(new URL('./node_modules/react', import.meta.url)),
      'react-dom': fileURLToPath(new URL('./node_modules/react-dom', import.meta.url))
    }
  },
  plugins: [
    tailwindcss(),
    reactRouter()
  ],
  server: {
    port: 3000
  },
  preview: {
    port: 3000
  },
  build: {
    rollupOptions: {
      external: /\/src\/presentation\/emails/
    }
  },
  optimizeDeps: {
    include: ['react', 'react-dom']
  },
  esbuild: {
    drop: ['console', 'debugger']
  },
  test: {
    watch: false,
    globals: true,
    environment: 'jsdom',
    setupFiles: './src/main/config/tests/setup-tests.ts',
    silent: true,
    alias: {
      '~': fileURLToPath(new URL('./src', import.meta.url)),
      'softo-design-system': fileURLToPath(
        new URL(
          './node_modules/softo-design-system/dist/softo-design-system.es.js',
          import.meta.url
        )
      )
    }
  }
})
