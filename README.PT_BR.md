# Softo Frontend Boilerplate

Um boilerplate moderno em React.js projetado para aplicações web escaláveis, construído com TypeScript e seguindo princípios de arquitetura limpa.

## Funcionalidades

- **Stack Moderna**: React 18, TypeScript, Vite
- **Autenticação**: Fluxo completo de autenticação com JWT, redefinição de senha e confirmação por email
- **Gerenciamento de Papéis**: Sistema de papéis e permissões de usuário
- **Internacionalização**: Suporte a múltiplos idiomas (EN/PT-BR)
- **Arquitetura Limpa**: Design orientado por domínio com clara separação de responsabilidades
- **Experiência do Desenvolvedor**: Biome, Lefthook, testes automatizados
- **Segurança de Tipos**: Implementação completa em TypeScript com tipagem rigorosa

## Início Rápido

### Pré-requisitos

- Node.js 18+
- pnpm (gerenciador de pacotes recomendado)

### Instalação

```bash
# Clone o repositório
git clone <repository-url>

# Instale as dependências
pnpm install

# Inicie o servidor de desenvolvimento
pnpm dev
```

### Configuração do Ambiente

1. Copie `.env.example` para `.env`
2. Configure suas variáveis de ambiente
3. Atualize os endpoints da API conforme necessário

## Estrutura do Projeto

```
src/
├── application/     # Lógica de negócio e casos de uso
├── domain/          # Entidades e regras centrais do negócio
├── infra/           # Integrações externas e adaptadores
├── main/            # Configuração da aplicação e injeção de dependência
└── presentation/    # Componentes de UI e páginas
```

## Scripts Disponíveis

- `pnpm dev` - Iniciar servidor de desenvolvimento
- `pnpm build` - Build para produção
- `pnpm test` - Executar testes
- `pnpm lint` - Executar linter
- `pnpm check-types` - Verificação de tipos TypeScript

## Stack Tecnológico

- **Frontend**: React, TypeScript, Vite
- **Estilização**: Tailwind CSS, Sass
- **Gerenciamento de Estado**: TanStack Query (React Query)
- **Testes**: Vitest, React Testing Library, MSW
- **Linting/Formatação**: Biome
- **Git Hooks**: Lefthook
- **Gerenciador de Pacotes**: pnpm

## Documentação

Documentação detalhada e guias estarão disponíveis na pasta `docs/` conforme o projeto evolui.

## Contribuindo

Por favor, siga os padrões estabelecidos e garanta que todos os testes passem antes de enviar pull requests.
