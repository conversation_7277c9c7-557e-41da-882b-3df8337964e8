import axios, { AxiosHeaders, type AxiosInstance } from 'axios'
import { API_URL } from '~/main/env'

import type { HttpClient, HttpRequest, HttpResponse } from './http-client'
import { HttpStatusCode } from './http-client'

export class AxiosHttpClientAdapter implements HttpClient {
  private readonly axiosInstance: AxiosInstance

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_URL
    })
  }

  async request({
    method,
    url,
    body,
    headers: _headers,
    queryParams,
    withCredentials
  }: HttpRequest): Promise<HttpResponse> {
    try {
      const axiosResponse = await this.axiosInstance.request({
        url,
        data: body,
        headers: this.getHeaders(_headers),
        method,
        params: queryParams,
        withCredentials
      })

      return {
        statusCode: axiosResponse.status,
        body: axiosResponse.data
      }
    } catch (error: any) {
      // Check if it's an HTTP error response (4xx, 5xx)
      if (error.response) {
        return {
          error: error.response.data,
          statusCode: error.response.status,
          message: error.response.statusText,
          body: undefined
        }
      }

      // Network error (ERR_CONNECTION_REFUSED, timeout, etc.)
      return {
        error: {
          code: error.code,
          message: error.message,
          type: 'NetworkError'
        },
        statusCode: HttpStatusCode.SERVER_ERROR,
        message: error.message
      }
    }
  }

  private getHeaders(headers: HeadersInit | undefined): AxiosHeaders {
    const axiosHeaders: AxiosHeaders = new AxiosHeaders()

    if (!headers) return axiosHeaders

    Array.from(new Headers(headers).entries()).forEach(([key, value]) => {
      axiosHeaders.set(key, value)
    })

    return axiosHeaders
  }
}
