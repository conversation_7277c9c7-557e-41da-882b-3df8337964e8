import { type RequestResponse } from '~/application/utils/http-response'
import { type DomainException } from '~/domain/exceptions/domain-exception'
import { type Either } from '~/domain/utils/either'

export interface HttpClient {
  request: <R = any, T = any>(
    params: HttpRequest<T>
  ) => Promise<HttpResponse<R>>
}

export type HttpRequest<T = any, C = any> = {
  body?: T
  headers?: Record<string, string> | Headers | HeadersInit
  queryParams?: Record<string, string>
  method: HttpMethod
  url: string
  context?: C
  withCredentials?: boolean
}

export type HandledHttpRequest<T = any> = HttpRequest<T> & {
  authorized: boolean
}

export interface HandledHttpClient {
  request: <R = any, T = any>(
    params: HandledHttpRequest<T>
  ) => Promise<HandledHttpResponse<R>>
}

export type HandledHttpResponse<T = any> = Either<
  DomainException,
  RequestResponse<T>
>

export type HttpResponse<T = any> = HttpError | HttpSuccess<T, Headers>

export type HttpSuccess<T = any, Headers = any> = {
  body: T
  error?: never
  message?: never
  statusCode: HttpStatusCode
  headers?: Headers
}

export type HttpError = {
  body?: never
  error: any
  message: string
  statusCode: HttpStatusCode
  headers?: Headers
}

export type HttpMethod = 'DELETE' | 'GET' | 'PATCH' | 'POST' | 'PUT'

export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  IS_CONFLICT = 409,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  SERVER_ERROR = 500
}
