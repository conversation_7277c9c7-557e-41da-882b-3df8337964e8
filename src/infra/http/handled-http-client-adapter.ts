import { RequestResponse } from '~/application/utils/http-response'

import {
  type HandledHttpClient,
  type HandledHttpRequest,
  type HandledHttpResponse,
  type HttpClient
} from './http-client'
import { NODE_ENV } from '~/main/env'

export class HandledHttpClientAdapter implements HandledHttpClient {
  constructor(private readonly httpClient: HttpClient) {}

  public async request<Response = any, Params = any>({
    authorized,
    ...params
  }: HandledHttpRequest<Params>): Promise<HandledHttpResponse<Response>> {
    if (authorized) {
      // Do whatever you need to do to authorize the request
    }

    const response = await this.httpClient.request({
      ...params,
      withCredentials: NODE_ENV === 'development'
    })

    return RequestResponse.handle(response)
  }
}
