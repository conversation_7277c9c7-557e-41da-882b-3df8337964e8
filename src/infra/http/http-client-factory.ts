import { AxiosHttpClientAdapter } from './axios-http-client-adapter'
import { LoggedHttpClient } from './logged-http-client'
import type { HttpClient } from './http-client'

export class HttpClientFactory {
  /**
   * Creates a basic HTTP client without logging
   */
  static createBasic(): HttpClient {
    return new AxiosHttpClientAdapter()
  }

  /**
   * Creates an HTTP client with comprehensive logging
   */
  static createWithLogging(loggerName?: string): HttpClient {
    const baseClient = new AxiosHttpClientAdapter()
    return new LoggedHttpClient(baseClient, {
      loggerName: loggerName || 'http-client',
      slowRequestThreshold: 2000,
      httpClientName: 'axios-http-client'
    })
  }

  /**
   * Creates an HTTP client with custom logging options
   */
  static createWithCustomLogging(options: {
    loggerName?: string
    logRequestBodies?: boolean
    logResponseBodies?: boolean
    slowRequestThreshold?: number
    httpClientName?: string
  }): HttpClient {
    const baseClient = new AxiosHttpClientAdapter()
    return new LoggedHttpClient(baseClient, options)
  }
}

// Example usage:
// const httpClient = HttpClientFactory.createWithLogging('auth-service')
// const response = await httpClient.request({ method: 'GET', url: '/users' })
