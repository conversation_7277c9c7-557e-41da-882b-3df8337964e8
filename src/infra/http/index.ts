import { HandledHttpClientAdapter } from '~/infra/http/handled-http-client-adapter'
import { type HandledHttpClient } from '~/infra/http/http-client'
import { HttpClientFactory } from './http-client-factory'
import { TenantHttpClientAdapter } from './tenant-http-client-adapter'

// New functions with logging capabilities
export const makeHttpClient = (loggerName?: string): HandledHttpClient =>
  new HandledHttpClientAdapter(
    HttpClientFactory.createWithLogging(loggerName || 'http-client')
  )

export const makeTenantHttpClient = (loggerName?: string): HandledHttpClient =>
  new TenantHttpClientAdapter(
    makeHttpClient(loggerName || 'tenant-http-client')
  )

// Advanced factory with custom options
export const makeCustomHttpClient = (options: {
  loggerName?: string
  logRequestBodies?: boolean
  logResponseBodies?: boolean
  slowRequestThreshold?: number
  enableLogging?: boolean
}): HandledHttpClient => {
  const { enableLogging = true, ...loggingOptions } = options

  const baseClient = enableLogging
    ? HttpClientFactory.createWithCustomLogging(loggingOptions)
    : HttpClientFactory.createBasic()

  return new HandledHttpClientAdapter(baseClient)
}

export const makeCustomTenantHttpClient = (options: {
  loggerName?: string
  logRequestBodies?: boolean
  logResponseBodies?: boolean
  slowRequestThreshold?: number
  enableLogging?: boolean
}): HandledHttpClient =>
  new TenantHttpClientAdapter(makeCustomHttpClient(options))
