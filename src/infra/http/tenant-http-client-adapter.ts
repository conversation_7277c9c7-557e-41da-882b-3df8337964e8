import type {
  HandledHttpClient,
  HandledHttpRequest,
  HandledHttpResponse
} from './http-client'
import { queryClient } from '~/main/config/query/queryClient'
import { loadTenantQuery } from '~/main/layouts/auth-layout/querys/load-tenant-query'
import type { LoadTenantModel } from '~/domain/models/load-tenant-model'

export class TenantHttpClientAdapter implements HandledHttpClient {
  constructor(private readonly httpClient: HandledHttpClient) {}

  public async request<Response>({
    headers: _headers,
    ...params
  }: HandledHttpRequest): Promise<HandledHttpResponse<Response>> {
    const loadTenantCachedData = await this.getTenantData()

    return this.httpClient.request({
      ...params,
      headers: {
        ..._headers,
        tenant_id: loadTenantCachedData?.id
      }
    })
  }

  private async getTenantData(): Promise<LoadTenantModel> {
    return queryClient.fetchQuery({
      ...loadTenantQuery()
    })
  }
}
