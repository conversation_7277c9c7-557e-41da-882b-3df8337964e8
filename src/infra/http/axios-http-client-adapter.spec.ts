import axios, { AxiosHeaders } from 'axios'
import { AxiosHttpClientAdapter } from './axios-http-client-adapter'
import { API_URL } from '~/main/env'
import type { Mocked } from 'vitest'
import {
  rand,
  randJSON,
  randNumber,
  randSentence,
  randVerb,
  randWord
} from '@ngneat/falso'

vi.mock('axios')

const mockedAxios = axios as Mocked<typeof axios>

const mockHttpRequest = () => ({
  method: rand(['DELETE', 'GET', 'PATCH', 'POST', 'PUT'] as const),
  url: `${API_URL}/${randWord()}`,
  body: randJSON({ minKeys: 1, maxKeys: 2 }),
  headers: new AxiosHeaders(randJSON({ minKeys: 1, maxKeys: 5 })),
  queryParams: randJSON({ minKeys: 1, maxKeys: 5 })
})

const mockAxiosResponse = (
  status: number = randNumber({ min: 200, max: 299 })
) => ({
  status,
  data: randJSON({ minKeys: 2, maxKeys: 5 })
})

const mockAxiosError = () => ({
  response: {
    data: randJSON({
      minKeys: 2,
      maxKeys: 5
    }),
    status: randVerb(),
    statusText: randSentence()
  }
})

const makeSut = () => {
  mockedAxios.create.mockReturnValue(mockedAxios)
  const axiosResponse = mockAxiosResponse()
  mockedAxios.request.mockResolvedValue(axiosResponse)
  const sut = new AxiosHttpClientAdapter()
  return { sut, axiosResponse }
}

describe('AxiosHttpClientAdapter', () => {
  it('Should return correct object on success', async () => {
    const httpRequest = mockHttpRequest()
    const { sut, axiosResponse } = makeSut()

    const response = await sut.request(httpRequest)

    expect(mockedAxios.request).toHaveBeenCalledWith({
      url: httpRequest.url,
      data: httpRequest.body,
      headers: httpRequest.headers,
      method: httpRequest.method,
      params: httpRequest.queryParams
    })
    expect(response).toEqual({
      statusCode: axiosResponse.status,
      body: axiosResponse.data
    })
  })

  it('Should return correct error object on failure', async () => {
    const axiosError = mockAxiosError()
    mockedAxios.request.mockRejectedValueOnce(axiosError)
    const { sut } = makeSut()

    const response = await sut.request(mockHttpRequest())

    expect(response).toEqual({
      error: axiosError.response?.data,
      statusCode: axiosError.response?.status,
      message: axiosError.response?.statusText
    })
  })
})
