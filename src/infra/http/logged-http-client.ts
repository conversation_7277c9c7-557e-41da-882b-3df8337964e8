import { getLogger } from '~/main/factories/make-logger'
import type { AppLogger } from '~/domain/entities/logger'
import type { HttpClient, HttpRequest, HttpResponse } from './http-client'
import {
  createLogContext,
  logHttpOperation,
  generateOperationId,
  getDataSize,
  type HttpLogContext
} from '~/presentation/utils/logging-utils'

export interface LoggedHttpClientOptions {
  loggerName?: string
  logRequestBodies?: boolean
  logResponseBodies?: boolean
  slowRequestThreshold?: number
  httpClientName?: string
}

export class LoggedHttpClient implements HttpClient {
  private readonly httpClient: HttpClient
  private readonly logger: AppLogger
  private readonly options: Required<LoggedHttpClientOptions>

  constructor(httpClient: HttpClient, options: LoggedHttpClientOptions = {}) {
    this.httpClient = httpClient
    this.logger = getLogger(options.loggerName || 'http-client')
    this.options = {
      loggerName: 'http-client',
      logRequestBodies: false,
      logResponseBodies: false,
      slowRequestThreshold: 2000,
      httpClientName: options.httpClientName || 'http-client',
      ...options
    }

    this.setupLogger()
  }

  private setupLogger(): void {
    this.logger.setGlobalContext({
      httpClient: this.options.httpClientName,
      component: this.options.httpClientName
    })
  }

  async request(httpRequest: HttpRequest): Promise<HttpResponse> {
    const requestContext = this.createRequestContext(httpRequest)
    const operationName = `${httpRequest.method?.toUpperCase()} ${httpRequest.url}`

    return await logHttpOperation(
      operationName,
      async () => await this.httpClient.request(httpRequest),
      this.logger,
      requestContext,
      {
        slowRequestThreshold: this.options.slowRequestThreshold
      }
    )
  }

  private createRequestContext(request: HttpRequest): HttpLogContext {
    const requestId = generateOperationId()

    const baseContext = createLogContext(
      'http-client',
      `${request.method?.toUpperCase()} ${request.url}`,
      {
        requestId,
        method: request.method?.toUpperCase(),
        url: request.url,
        hasQueryParams: !!request.queryParams,
        withCredentials: !!request.withCredentials
      }
    )

    if (this.options.logRequestBodies && request.body) {
      baseContext.bodySize = getDataSize(request.body)
    }

    return {
      operationId: requestId,
      requestId,
      method: request.method?.toUpperCase() || '',
      url: request.url || '',
      ...baseContext
    }
  }
}
