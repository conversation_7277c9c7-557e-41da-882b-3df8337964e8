import { LocalStorageCacheStorage } from './local-storage-cache-storage'

class LocalStorageMock {
  private store: Record<string, string> = {}

  clear() {
    this.store = {}
  }

  getItem(key: string) {
    return this.store[key] || null
  }

  setItem(key: string, value: string) {
    this.store[key] = value
  }

  removeItem(key: string) {
    delete this.store[key]
  }
}

// @ts-ignore
global.localStorage = new LocalStorageMock()

const makeSut = () => {
  return new LocalStorageCacheStorage()
}

describe('LocalStorageCacheStorage', () => {
  it('Should return correct object on get() ', () => {
    const key = 'key'
    const fakerObj = {
      key: 'value'
    }
    vi.spyOn(localStorage, 'getItem').mockReturnValueOnce(
      JSON.stringify(fakerObj)
    )
    const sut = makeSut()

    const response = sut.get(key)

    expect(response).toEqual(fakerObj)
    expect(localStorage.getItem).toHaveBeenCalledWith(key)
  })

  it('Should return undefined on get() if value is null', () => {
    const key = 'key'
    vi.spyOn(localStorage, 'getItem').mockReturnValueOnce(null)
    const sut = makeSut()

    const response = sut.get(key)

    expect(response).toBeUndefined()
    expect(localStorage.getItem).toHaveBeenCalledWith(key)
  })

  it('Should call localStorage.setItem() with correct values', () => {
    const key = 'key'
    const fakerObj = {
      key: 'value'
    }
    const sut = makeSut()
    const setItemSpy = vi.spyOn(localStorage, 'setItem')

    sut.set(key, fakerObj)

    expect(setItemSpy).toHaveBeenCalledWith(key, JSON.stringify(fakerObj))
  })

  it('Should call localStorage.removeItem() if value is null', () => {
    const key = 'key'
    const sut = makeSut()
    const removeItemSpy = vi.spyOn(localStorage, 'removeItem')

    sut.set(key, null)

    expect(removeItemSpy).toHaveBeenCalledWith(key)
  })
})
