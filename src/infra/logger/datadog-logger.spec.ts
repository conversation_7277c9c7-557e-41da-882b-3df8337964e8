import { DatadogLogger } from './datadog-logger'
import { LogLevel, type LogEntry } from '~/domain/entities/logger'
import { datadogLogs } from '@datadog/browser-logs'
import { randWord, randJSON, randSentence } from '@ngneat/falso'
import type { Mocked } from 'vitest'

vi.mock('@datadog/browser-logs')

const mockNodeEnv = vi.hoisted(() => ({ NODE_ENV: 'test' }))
vi.mock('~/main/env', () => mockNodeEnv)

const mockedDatadogLogs = datadogLogs as Mocked<typeof datadogLogs>

const mockDatadogConfig = () => ({
  clientToken: randWord(),
  site: 'datadoghq.com' as const,
  service: randWord(),
  env: randWord(),
  version: randWord()
})

const mockLogEntry = (level: LogLevel = LogLevel.INFO): LogEntry => ({
  timestamp: new Date().toISOString(),
  level,
  message: randSentence(),
  context: randJSON({ minKeys: 1, maxKeys: 3 }),
  service_name: randWord()
})

const makeSut = (serviceName: string = randWord()) => {
  const sut = new DatadogLogger(serviceName)
  return { sut, serviceName }
}

describe('DatadogLogger', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(DatadogLogger as any).initialized = false
    mockedDatadogLogs.init = vi.fn()
    mockedDatadogLogs.logger = {
      log: vi.fn()
    } as any
  })

  describe('constructor', () => {
    it('should create instance with service name', () => {
      const serviceName = randWord()
      const { sut } = makeSut(serviceName)

      expect(sut).toBeInstanceOf(DatadogLogger)
    })
  })

  describe('init', () => {
    it('should initialize datadog with valid config', () => {
      const config = mockDatadogConfig()

      DatadogLogger.init(config)

      expect(mockedDatadogLogs.init).toHaveBeenCalledWith({
        clientToken: config.clientToken,
        forwardErrorsToLogs: true,
        sessionSampleRate: 100,
        site: config.site,
        service: config.service,
        env: config.env,
        version: config.version
      })
    })

    it('should initialize datadog with minimal config', () => {
      const config = {
        clientToken: randWord(),
        site: 'datadoghq.com' as const
      }

      DatadogLogger.init(config)

      expect(mockedDatadogLogs.init).toHaveBeenCalledWith({
        clientToken: config.clientToken,
        forwardErrorsToLogs: true,
        sessionSampleRate: 100,
        site: config.site
      })
    })

    it('should throw error when clientToken is missing', () => {
      const config = {
        site: 'datadoghq.com' as const
      }

      expect(() => DatadogLogger.init(config)).toThrow(
        'Failed to initialize DatadogLogger'
      )
    })

    it('should throw error when clientToken is empty', () => {
      const config = {
        clientToken: '',
        site: 'datadoghq.com' as const
      }

      expect(() => DatadogLogger.init(config)).toThrow(
        'Failed to initialize DatadogLogger'
      )
    })

    it('should throw error when site is invalid', () => {
      const config = {
        clientToken: randWord(),
        site: 'invalid-site.com'
      }

      expect(() => DatadogLogger.init(config)).toThrow(
        'Failed to initialize DatadogLogger'
      )
    })

    it('should not re-initialize when already initialized', () => {
      const config = mockDatadogConfig()
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(vi.fn())

      DatadogLogger.init(config)
      DatadogLogger.init(config)

      expect(mockedDatadogLogs.init).toHaveBeenCalledTimes(1)
      expect(consoleSpy).toHaveBeenCalledWith(
        'DatadogLogger is already initialized. Skipping re-initialization.'
      )

      consoleSpy.mockRestore()
    })

    it('should handle datadog init error', () => {
      const config = mockDatadogConfig()
      const error = new Error('Datadog init failed')
      mockedDatadogLogs.init.mockImplementation(() => {
        throw error
      })

      expect(() => DatadogLogger.init(config)).toThrow(
        'Failed to initialize DatadogLogger: Datadog init failed'
      )
    })
  })

  describe('setContext', () => {
    it('should set context value', () => {
      const { sut } = makeSut()
      const key = randWord()
      const value = randJSON()

      sut.setContext(key, value)

      expect(sut.getContext()).toEqual({ [key]: value })
    })

    it('should update existing context value', () => {
      const { sut } = makeSut()
      const key = randWord()
      const initialValue = randWord()
      const newValue = randWord()

      sut.setContext(key, initialValue)
      sut.setContext(key, newValue)

      expect(sut.getContext()).toEqual({ [key]: newValue })
    })

    it('should set multiple context values', () => {
      const { sut } = makeSut()
      const key1 = randWord()
      const key2 = randWord()
      const value1 = randJSON()
      const value2 = randJSON()

      sut.setContext(key1, value1)
      sut.setContext(key2, value2)

      expect(sut.getContext()).toEqual({
        [key1]: value1,
        [key2]: value2
      })
    })
  })

  describe('getContext', () => {
    it('should return empty object when no context set', () => {
      const { sut } = makeSut()

      expect(sut.getContext()).toEqual({})
    })

    it('should return copy of context', () => {
      const { sut } = makeSut()
      const key = randWord()
      const value = randJSON()

      sut.setContext(key, value)
      const context = sut.getContext()
      context.newKey = 'newValue'

      expect(sut.getContext()).toEqual({ [key]: value })
    })
  })

  describe('log', () => {
    beforeEach(() => {
      DatadogLogger.init(mockDatadogConfig())
    })

    afterEach(() => {
      mockNodeEnv.NODE_ENV = 'test'
    })

    it('should log to datadog in production with ERROR level', () => {
      mockNodeEnv.NODE_ENV = 'production'

      const { sut, serviceName } = makeSut()
      const logEntry = mockLogEntry(LogLevel.ERROR)
      const context = randJSON()

      sut.setContext('contextKey', context)
      sut.log(logEntry)

      expect(mockedDatadogLogs.logger.log).toHaveBeenCalledWith(
        logEntry.message,
        {
          contextKey: context,
          ...logEntry.context,
          level: 'error',
          service_name: serviceName,
          timestamp: expect.any(String)
        }
      )
    })

    it('should log to datadog in production with WARN level', () => {
      mockNodeEnv.NODE_ENV = 'production'

      const { sut, serviceName } = makeSut()
      const logEntry = mockLogEntry(LogLevel.WARN)

      sut.log(logEntry)

      expect(mockedDatadogLogs.logger.log).toHaveBeenCalledWith(
        logEntry.message,
        {
          ...logEntry.context,
          level: 'warn',
          service_name: serviceName,
          timestamp: expect.any(String)
        }
      )
    })

    it('should log to datadog in production with FATAL level', () => {
      mockNodeEnv.NODE_ENV = 'production'

      const { sut, serviceName } = makeSut()
      const logEntry = mockLogEntry(LogLevel.FATAL)

      sut.log(logEntry)

      expect(mockedDatadogLogs.logger.log).toHaveBeenCalledWith(
        logEntry.message,
        {
          ...logEntry.context,
          level: 'fatal',
          service_name: serviceName,
          timestamp: expect.any(String)
        }
      )
    })

    it('should not log to datadog in production with INFO level', () => {
      mockNodeEnv.NODE_ENV = 'production'

      const { sut } = makeSut()
      const logEntry = mockLogEntry(LogLevel.INFO)

      sut.log(logEntry)

      expect(mockedDatadogLogs.logger.log).not.toHaveBeenCalled()
    })

    it('should not log to datadog in production with DEBUG level', () => {
      mockNodeEnv.NODE_ENV = 'production'

      const { sut } = makeSut()
      const logEntry = mockLogEntry(LogLevel.DEBUG)

      sut.log(logEntry)

      expect(mockedDatadogLogs.logger.log).not.toHaveBeenCalled()
    })

    it('should log to console in non-production environment', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(vi.fn())
      const { sut, serviceName } = makeSut()
      const logEntry = mockLogEntry(LogLevel.INFO)
      const context = randJSON()

      sut.setContext('contextKey', context)
      sut.log(logEntry)

      expect(consoleSpy).toHaveBeenCalledTimes(1)

      const loggedData = JSON.parse(consoleSpy.mock.calls[0][0])
      expect(loggedData).toEqual({
        timestamp: expect.any(String),
        level: logEntry.level,
        message: logEntry.message,
        context: expect.objectContaining({
          contextKey: context
        }),
        service_name: serviceName
      })

      consoleSpy.mockRestore()
    })

    it('should merge entry context with instance context', () => {
      mockNodeEnv.NODE_ENV = 'production'

      const { sut } = makeSut()
      const instanceContext = { instanceKey: randWord() }
      const entryContext = { entryKey: randWord() }
      const logEntry = mockLogEntry(LogLevel.ERROR)
      logEntry.context = entryContext

      sut.setContext('instanceKey', instanceContext.instanceKey)
      sut.log(logEntry)

      expect(mockedDatadogLogs.logger.log).toHaveBeenCalledWith(
        logEntry.message,
        expect.objectContaining({
          instanceKey: instanceContext.instanceKey,
          entryKey: entryContext.entryKey
        })
      )
    })

    it('should prioritize entry context over instance context when keys conflict', () => {
      mockNodeEnv.NODE_ENV = 'production'

      const { sut } = makeSut()
      const conflictKey = 'conflictKey'
      const instanceValue = randWord()
      const entryValue = randWord()
      const logEntry = mockLogEntry(LogLevel.ERROR)
      logEntry.context = { [conflictKey]: entryValue }

      sut.setContext(conflictKey, instanceValue)
      sut.log(logEntry)

      expect(mockedDatadogLogs.logger.log).toHaveBeenCalledWith(
        logEntry.message,
        expect.objectContaining({
          [conflictKey]: entryValue
        })
      )
    })
  })
})
