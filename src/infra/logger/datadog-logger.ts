import { datadogLogs, type LogsInitConfiguration } from '@datadog/browser-logs'
import { LogLevel, type LogEntry, type Logger } from '~/domain/entities/logger'
import { NODE_ENV } from '~/main/env'
import { z } from 'zod'

const DD_SITES = [
  'datadoghq.com',
  'us3.datadoghq.com',
  'us5.datadoghq.com',
  'datadoghq.eu',
  'ddog-gov.com',
  'ap1.datadoghq.com',
  'ap2.datadoghq.com'
] as const

const datadogConfigSchema = z
  .object({
    clientToken: z
      .string()
      .min(1, 'Client token is required and cannot be empty'),
    site: z.enum(DD_SITES),
    service: z.string().optional(),
    env: z.string().optional(),
    version: z.string().optional()
  })
  .transform((data) => {
    const config: LogsInitConfiguration = {
      clientToken: data.clientToken,
      forwardErrorsToLogs: true,
      sessionSampleRate: 100
    }

    if (data.site) config.site = data.site
    if (data.service) config.service = data.service
    if (data.env) config.env = data.env
    if (data.version) config.version = data.version

    return config as LogsInitConfiguration
  })

export class DatadogLogger implements Logger {
  private context: Record<string, any> = {}
  private serviceName: string
  private static initialized = false

  constructor(serviceName: string) {
    this.serviceName = serviceName
  }

  /**
   * Initializes the Datadog logger with the provided configuration.
   * This method should be called once during application startup.
   *
   * @param config - The Datadog configuration object
   * @throws Error if initialization fails or if configuration is invalid
   */
  static init(config: unknown): void {
    if (this.initialized) {
      console.warn(
        'DatadogLogger is already initialized. Skipping re-initialization.'
      )
      return
    }

    try {
      const initConfig = this.validateAndBuildConfig(config)

      datadogLogs.init(initConfig)
      this.initialized = true

      if (NODE_ENV === 'development') {
        const inputConfig = config as any
        console.log('DatadogLogger initialized successfully', {
          service: inputConfig.service,
          env: inputConfig.env,
          version: inputConfig.version
        })
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      throw new Error(`Failed to initialize DatadogLogger: ${errorMessage}`)
    }
  }

  /**
   * Validates and builds the Datadog logs initialization configuration in a single pass
   * @param config - Raw configuration object to validate and transform
   * @returns Validated and transformed LogsInitConfiguration
   * @throws Error if validation fails
   */
  private static validateAndBuildConfig(
    config: unknown
  ): LogsInitConfiguration {
    try {
      return datadogConfigSchema.parse(config)
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors
          .map((err) => `${err.path.join('.')}: ${err.message}`)
          .join(', ')
        throw new Error(
          `DatadogLogger configuration validation failed: ${errorMessages}`
        )
      }
      throw error
    }
  }

  setContext(key: string, value: any): void {
    this.context[key] = value
  }

  getContext(): Record<string, any> {
    return { ...this.context }
  }

  log(entry: LogEntry): void {
    if (this.shouldLog(entry.level)) {
      datadogLogs.logger.log(entry.message, {
        ...this.context,
        ...entry.context,
        level: entry.level.toLowerCase(),
        service_name: this.serviceName,
        timestamp: new Date().toISOString()
      })
    } else if (NODE_ENV !== 'production') {
      console.log(
        JSON.stringify({
          timestamp: new Date().toISOString(),
          level: entry.level,
          message: entry.message,
          context: { ...this.context, ...entry.context },
          service_name: this.serviceName
        })
      )
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return (
      NODE_ENV === 'production' &&
      ([LogLevel.FATAL, LogLevel.ERROR, LogLevel.WARN] as LogLevel[]).includes(
        level
      )
    )
  }
}
