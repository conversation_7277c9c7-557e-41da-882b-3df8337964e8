import { LogLevel, type LogEntry, type Logger } from '~/domain/entities/logger'
import { NODE_ENV } from '~/main/env'

export class ConsoleLogger implements Logger {
  private context: Record<string, any> = {}
  private serviceName: string

  constructor(serviceName: string) {
    this.serviceName = serviceName
  }

  setContext(key: string, value: any): void {
    this.context[key] = value
  }

  getContext(): Record<string, any> {
    return { ...this.context }
  }

  log(entry: LogEntry): void {
    if (this.shouldLog(entry.level)) {
      const logData = {
        timestamp: new Date().toISOString(),
        level: entry.level,
        message: entry.message,
        context: { ...this.context, ...entry.context },
        service_name: this.serviceName
      }

      this.formatConsoleOutput(entry.level, logData)
    }
  }

  private shouldLog(level: LogLevel): boolean {
    if (NODE_ENV === 'production') {
      return (
        [LogLevel.FATAL, LogLevel.ERROR, LogLevel.WARN] as LogLevel[]
      ).includes(level)
    }
    return true
  }

  private formatConsoleOutput(level: LogLevel, logData: any): void {
    const timestamp = logData.timestamp
    const message = logData.message
    const context = logData.context
    const serviceName = logData.service_name

    const logString = `[${timestamp}] [${serviceName}] ${level}: ${message}`

    switch (level) {
      case LogLevel.FATAL:
      case LogLevel.ERROR:
        console.error(logString, context)
        break
      case LogLevel.WARN:
        console.warn(logString, context)
        break
      case LogLevel.INFO:
        console.info(logString, context)
        break
      case LogLevel.DEBUG:
      case LogLevel.TRACE:
        console.debug(logString, context)
        break
      default:
        console.log(logString, context)
    }
  }
}
