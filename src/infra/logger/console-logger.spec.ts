import { ConsoleLogger } from './console-logger'
import { LogLevel, type LogEntry } from '~/domain/entities/logger'
import { randWord, randJSON, randSentence } from '@ngneat/falso'

const mockNodeEnv = vi.hoisted(() => ({ NODE_ENV: 'test' }))
vi.mock('~/main/env', () => mockNodeEnv)

const mockLogEntry = (level: LogLevel = LogLevel.INFO): LogEntry => ({
  timestamp: new Date().toISOString(),
  level,
  message: randSentence(),
  context: randJSON({ minKeys: 1, maxKeys: 3 }),
  service_name: randWord()
})

const makeSut = (serviceName: string = randWord()) => {
  const sut = new ConsoleLogger(serviceName)
  return { sut, serviceName }
}

describe('ConsoleLogger', () => {
  let consoleErrorSpy: ReturnType<typeof vi.spyOn>
  let consoleWarnSpy: ReturnType<typeof vi.spyOn>
  let consoleInfoSpy: ReturnType<typeof vi.spyOn>
  let consoleDebugSpy: ReturnType<typeof vi.spyOn>
  let consoleLogSpy: ReturnType<typeof vi.spyOn>

  beforeEach(() => {
    vi.clearAllMocks()

    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(vi.fn())
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(vi.fn())
    consoleInfoSpy = vi.spyOn(console, 'info').mockImplementation(vi.fn())
    consoleDebugSpy = vi.spyOn(console, 'debug').mockImplementation(vi.fn())
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(vi.fn())
  })

  afterEach(() => {
    mockNodeEnv.NODE_ENV = 'test'

    consoleErrorSpy.mockRestore()
    consoleWarnSpy.mockRestore()
    consoleInfoSpy.mockRestore()
    consoleDebugSpy.mockRestore()
    consoleLogSpy.mockRestore()
  })

  describe('constructor', () => {
    it('should create instance with service name', () => {
      const serviceName = randWord()
      const { sut } = makeSut(serviceName)

      expect(sut).toBeInstanceOf(ConsoleLogger)
    })
  })

  describe('setContext', () => {
    it('should set context value', () => {
      const { sut } = makeSut()
      const key = randWord()
      const value = randJSON()

      sut.setContext(key, value)

      expect(sut.getContext()).toEqual({ [key]: value })
    })

    it('should update existing context value', () => {
      const { sut } = makeSut()
      const key = randWord()
      const initialValue = randWord()
      const newValue = randWord()

      sut.setContext(key, initialValue)
      sut.setContext(key, newValue)

      expect(sut.getContext()).toEqual({ [key]: newValue })
    })

    it('should set multiple context values', () => {
      const { sut } = makeSut()
      const key1 = randWord()
      const key2 = randWord()
      const value1 = randJSON()
      const value2 = randJSON()

      sut.setContext(key1, value1)
      sut.setContext(key2, value2)

      expect(sut.getContext()).toEqual({
        [key1]: value1,
        [key2]: value2
      })
    })
  })

  describe('getContext', () => {
    it('should return empty object when no context set', () => {
      const { sut } = makeSut()

      expect(sut.getContext()).toEqual({})
    })

    it('should return copy of context', () => {
      const { sut } = makeSut()
      const key = randWord()
      const value = randJSON()

      sut.setContext(key, value)
      const context = sut.getContext()
      context.newKey = 'newValue'

      expect(sut.getContext()).toEqual({ [key]: value })
    })
  })

  describe('log', () => {
    describe('in production environment', () => {
      beforeEach(() => {
        mockNodeEnv.NODE_ENV = 'production'
      })

      it('should log FATAL level to console.error', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.FATAL)
        const context = randJSON()

        sut.setContext('contextKey', context)
        sut.log(logEntry)

        expect(consoleErrorSpy).toHaveBeenCalledTimes(1)
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          expect.stringContaining(
            `[${serviceName}] FATAL: ${logEntry.message}`
          ),
          expect.objectContaining({
            contextKey: context,
            ...logEntry.context
          })
        )
      })

      it('should log ERROR level to console.error', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.ERROR)

        sut.log(logEntry)

        expect(consoleErrorSpy).toHaveBeenCalledTimes(1)
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          expect.stringContaining(
            `[${serviceName}] ERROR: ${logEntry.message}`
          ),
          logEntry.context
        )
      })

      it('should log WARN level to console.warn', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.WARN)

        sut.log(logEntry)

        expect(consoleWarnSpy).toHaveBeenCalledTimes(1)
        expect(consoleWarnSpy).toHaveBeenCalledWith(
          expect.stringContaining(`[${serviceName}] WARN: ${logEntry.message}`),
          logEntry.context
        )
      })

      it('should not log INFO level', () => {
        const { sut } = makeSut()
        const logEntry = mockLogEntry(LogLevel.INFO)

        sut.log(logEntry)

        expect(consoleInfoSpy).not.toHaveBeenCalled()
        expect(consoleLogSpy).not.toHaveBeenCalled()
        expect(consoleDebugSpy).not.toHaveBeenCalled()
      })

      it('should not log DEBUG level', () => {
        const { sut } = makeSut()
        const logEntry = mockLogEntry(LogLevel.DEBUG)

        sut.log(logEntry)

        expect(consoleDebugSpy).not.toHaveBeenCalled()
        expect(consoleLogSpy).not.toHaveBeenCalled()
        expect(consoleInfoSpy).not.toHaveBeenCalled()
      })

      it('should not log TRACE level', () => {
        const { sut } = makeSut()
        const logEntry = mockLogEntry(LogLevel.TRACE)

        sut.log(logEntry)

        expect(consoleDebugSpy).not.toHaveBeenCalled()
        expect(consoleLogSpy).not.toHaveBeenCalled()
        expect(consoleInfoSpy).not.toHaveBeenCalled()
      })
    })

    describe('in non-production environment', () => {
      it('should log FATAL level to console.error', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.FATAL)

        sut.log(logEntry)

        expect(consoleErrorSpy).toHaveBeenCalledTimes(1)
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          expect.stringContaining(
            `[${serviceName}] FATAL: ${logEntry.message}`
          ),
          logEntry.context
        )
      })

      it('should log ERROR level to console.error', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.ERROR)

        sut.log(logEntry)

        expect(consoleErrorSpy).toHaveBeenCalledTimes(1)
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          expect.stringContaining(
            `[${serviceName}] ERROR: ${logEntry.message}`
          ),
          logEntry.context
        )
      })

      it('should log WARN level to console.warn', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.WARN)

        sut.log(logEntry)

        expect(consoleWarnSpy).toHaveBeenCalledTimes(1)
        expect(consoleWarnSpy).toHaveBeenCalledWith(
          expect.stringContaining(`[${serviceName}] WARN: ${logEntry.message}`),
          logEntry.context
        )
      })

      it('should log INFO level to console.info', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.INFO)

        sut.log(logEntry)

        expect(consoleInfoSpy).toHaveBeenCalledTimes(1)
        expect(consoleInfoSpy).toHaveBeenCalledWith(
          expect.stringContaining(`[${serviceName}] INFO: ${logEntry.message}`),
          logEntry.context
        )
      })

      it('should log DEBUG level to console.debug', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.DEBUG)

        sut.log(logEntry)

        expect(consoleDebugSpy).toHaveBeenCalledTimes(1)
        expect(consoleDebugSpy).toHaveBeenCalledWith(
          expect.stringContaining(
            `[${serviceName}] DEBUG: ${logEntry.message}`
          ),
          logEntry.context
        )
      })

      it('should log TRACE level to console.debug', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.TRACE)

        sut.log(logEntry)

        expect(consoleDebugSpy).toHaveBeenCalledTimes(1)
        expect(consoleDebugSpy).toHaveBeenCalledWith(
          expect.stringContaining(
            `[${serviceName}] TRACE: ${logEntry.message}`
          ),
          logEntry.context
        )
      })
    })

    describe('log formatting', () => {
      it('should include timestamp in log output', () => {
        const { sut } = makeSut()
        const logEntry = mockLogEntry(LogLevel.INFO)

        sut.log(logEntry)

        expect(consoleInfoSpy).toHaveBeenCalledWith(
          expect.stringMatching(
            /^\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\]/
          ),
          expect.any(Object)
        )
      })

      it('should include service name in log output', () => {
        const serviceName = randWord()
        const { sut } = makeSut(serviceName)
        const logEntry = mockLogEntry(LogLevel.INFO)

        sut.log(logEntry)

        expect(consoleInfoSpy).toHaveBeenCalledWith(
          expect.stringContaining(`[${serviceName}]`),
          expect.any(Object)
        )
      })

      it('should include log level in log output', () => {
        const { sut } = makeSut()
        const logEntry = mockLogEntry(LogLevel.INFO)

        sut.log(logEntry)

        expect(consoleInfoSpy).toHaveBeenCalledWith(
          expect.stringContaining('INFO:'),
          expect.any(Object)
        )
      })

      it('should include message in log output', () => {
        const { sut } = makeSut()
        const logEntry = mockLogEntry(LogLevel.INFO)

        sut.log(logEntry)

        expect(consoleInfoSpy).toHaveBeenCalledWith(
          expect.stringContaining(logEntry.message),
          expect.any(Object)
        )
      })
    })

    describe('context merging', () => {
      it('should merge entry context with instance context', () => {
        const { sut } = makeSut()
        const instanceContext = { instanceKey: randWord() }
        const entryContext = { entryKey: randWord() }
        const logEntry = mockLogEntry(LogLevel.INFO)
        logEntry.context = entryContext

        sut.setContext('instanceKey', instanceContext.instanceKey)
        sut.log(logEntry)

        expect(consoleInfoSpy).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            instanceKey: instanceContext.instanceKey,
            entryKey: entryContext.entryKey
          })
        )
      })

      it('should prioritize entry context over instance context when keys conflict', () => {
        const { sut } = makeSut()
        const conflictKey = 'conflictKey'
        const instanceValue = randWord()
        const entryValue = randWord()
        const logEntry = mockLogEntry(LogLevel.INFO)
        logEntry.context = { [conflictKey]: entryValue }

        sut.setContext(conflictKey, instanceValue)
        sut.log(logEntry)

        expect(consoleInfoSpy).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            [conflictKey]: entryValue
          })
        )
      })

      it('should use instance context when entry context is empty', () => {
        const { sut } = makeSut()
        const instanceContext = { instanceKey: randWord() }
        const logEntry = mockLogEntry(LogLevel.INFO)
        logEntry.context = {}

        sut.setContext('instanceKey', instanceContext.instanceKey)
        sut.log(logEntry)

        expect(consoleInfoSpy).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            instanceKey: instanceContext.instanceKey
          })
        )
      })
    })

    describe('fallback console method', () => {
      it('should use console.log for unknown log levels', () => {
        const { sut, serviceName } = makeSut()
        const logEntry = mockLogEntry(LogLevel.INFO)
        logEntry.level = 'UNKNOWN' as LogLevel

        sut.log(logEntry)

        expect(consoleLogSpy).toHaveBeenCalledTimes(1)
        expect(consoleLogSpy).toHaveBeenCalledWith(
          expect.stringContaining(
            `[${serviceName}] UNKNOWN: ${logEntry.message}`
          ),
          logEntry.context
        )
      })
    })
  })
})
