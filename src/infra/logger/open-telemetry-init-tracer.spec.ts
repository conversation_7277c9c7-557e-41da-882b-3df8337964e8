import {
  initTracer,
  getTracer,
  createCustomSpan
} from './open-telemetry-init-tracer'
import { WebTracerProvider } from '@opentelemetry/sdk-trace-web'
import { resourceFromAttributes } from '@opentelemetry/resources'
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base'
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http'
import { registerInstrumentations } from '@opentelemetry/instrumentation'
import { FetchInstrumentation } from '@opentelemetry/instrumentation-fetch'
import { XMLHttpRequestInstrumentation } from '@opentelemetry/instrumentation-xml-http-request'
import { UserInteractionInstrumentation } from '@opentelemetry/instrumentation-user-interaction'
import { trace } from '@opentelemetry/api'
import { randWord, randUrl, randSemver } from '@ngneat/falso'

const { mockProvider, mockTracer, mockSpan } = vi.hoisted(() => ({
  mockProvider: {
    register: vi.fn(),
    shutdown: vi.fn(),
    forceFlush: vi.fn()
  },
  mockTracer: {
    startActiveSpan: vi.fn(),
    startSpan: vi.fn()
  },
  mockSpan: {
    end: vi.fn(),
    recordException: vi.fn(),
    setStatus: vi.fn(),
    setAttribute: vi.fn(),
    setAttributes: vi.fn()
  }
}))

vi.mock('@opentelemetry/sdk-trace-web', () => ({
  WebTracerProvider: vi.fn().mockImplementation(() => mockProvider)
}))

vi.mock('@opentelemetry/resources', () => ({
  resourceFromAttributes: vi.fn().mockReturnValue({})
}))

vi.mock('@opentelemetry/sdk-trace-base', () => ({
  BatchSpanProcessor: vi.fn().mockImplementation(() => ({}))
}))

vi.mock('@opentelemetry/exporter-trace-otlp-http', () => ({
  OTLPTraceExporter: vi.fn().mockImplementation(() => ({}))
}))

vi.mock('@opentelemetry/context-zone', () => ({
  ZoneContextManager: vi.fn().mockImplementation(() => ({}))
}))

vi.mock('@opentelemetry/instrumentation', () => ({
  registerInstrumentations: vi.fn()
}))

vi.mock('@opentelemetry/instrumentation-fetch', () => ({
  FetchInstrumentation: vi.fn().mockImplementation(() => ({}))
}))

vi.mock('@opentelemetry/instrumentation-xml-http-request', () => ({
  XMLHttpRequestInstrumentation: vi.fn().mockImplementation(() => ({}))
}))

vi.mock('@opentelemetry/instrumentation-document-load', () => ({
  DocumentLoadInstrumentation: vi.fn().mockImplementation(() => ({}))
}))

vi.mock('@opentelemetry/instrumentation-user-interaction', () => ({
  UserInteractionInstrumentation: vi.fn().mockImplementation(() => ({}))
}))

vi.mock('@opentelemetry/core', () => ({
  CompositePropagator: vi.fn().mockImplementation(() => ({})),
  W3CTraceContextPropagator: vi.fn().mockImplementation(() => ({})),
  W3CBaggagePropagator: vi.fn().mockImplementation(() => ({}))
}))

vi.mock('@opentelemetry/api', () => ({
  trace: {
    getTracer: vi.fn().mockReturnValue(mockTracer),
    setGlobalTracerProvider: vi.fn()
  }
}))

const mockImportMeta = vi.hoisted(() => ({ env: { MODE: 'test' } }))
vi.stubGlobal('import', { meta: mockImportMeta })

const mockTracerConfig = () => ({
  serviceName: randWord(),
  serviceVersion: randSemver(),
  environment: randWord(),
  endpoint: randUrl(),
  debug: false
})

describe('OpenTelemetry Tracer', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    mockTracer.startActiveSpan.mockImplementation((_name, callback) => {
      return callback(mockSpan)
    })
  })

  describe('initTracer', () => {
    it('should initialize with minimal config', () => {
      const serviceName = randWord()

      initTracer(serviceName)

      expect(resourceFromAttributes).toHaveBeenCalledWith({
        'service.name': serviceName,
        'service.version': '1.0.0',
        'deployment.environment.name': 'test'
      })
      expect(WebTracerProvider).toHaveBeenCalledWith({
        resource: {},
        spanProcessors: [{}]
      })
      expect(mockProvider.register).toHaveBeenCalledTimes(1)
      expect(trace.setGlobalTracerProvider).toHaveBeenCalledWith(mockProvider)
    })

    it('should initialize with full config', () => {
      const config = mockTracerConfig()

      initTracer(config.serviceName, config)

      expect(resourceFromAttributes).toHaveBeenCalledWith({
        'service.name': config.serviceName,
        'service.version': config.serviceVersion,
        'deployment.environment.name': config.environment
      })
      expect(OTLPTraceExporter).toHaveBeenCalledWith({
        url: config.endpoint,
        headers: {}
      })
    })

    it('should use default values for missing config properties', () => {
      const serviceName = randWord()
      const partialConfig = {
        endpoint: randUrl()
      }

      initTracer(serviceName, partialConfig)

      expect(resourceFromAttributes).toHaveBeenCalledWith({
        'service.name': serviceName,
        'service.version': '1.0.0',
        'deployment.environment.name': 'test'
      })
      expect(OTLPTraceExporter).toHaveBeenCalledWith({
        url: partialConfig.endpoint,
        headers: {}
      })
    })

    it('should configure BatchSpanProcessor with correct options', () => {
      const serviceName = randWord()

      initTracer(serviceName)

      expect(BatchSpanProcessor).toHaveBeenCalledWith(
        {},
        {
          maxExportBatchSize: 50,
          scheduledDelayMillis: 500,
          exportTimeoutMillis: 30000,
          maxQueueSize: 100
        }
      )
    })

    it('should register instrumentations with correct configuration', () => {
      const serviceName = randWord()

      initTracer(serviceName)

      expect(registerInstrumentations).toHaveBeenCalledWith({
        instrumentations: expect.arrayContaining([
          expect.any(Object),
          expect.any(Object),
          expect.any(Object),
          expect.any(Object)
        ])
      })
    })

    it('should configure FetchInstrumentation with correct options', () => {
      const serviceName = randWord()

      initTracer(serviceName)

      expect(FetchInstrumentation).toHaveBeenCalledWith({
        propagateTraceHeaderCorsUrls: [/.*/],
        clearTimingResources: true,
        ignoreUrls: [
          /localhost:8090\/sockjs-node/,
          /_next\/static/,
          /favicon\.ico/,
          /\.hot-update\./
        ]
      })
    })

    it('should configure XMLHttpRequestInstrumentation with correct options', () => {
      const serviceName = randWord()

      initTracer(serviceName)

      expect(XMLHttpRequestInstrumentation).toHaveBeenCalledWith({
        propagateTraceHeaderCorsUrls: [/.*/],
        ignoreUrls: [
          /localhost:8090\/sockjs-node/,
          /_next\/static/,
          /favicon\.ico/,
          /\.hot-update\./
        ]
      })
    })

    it('should configure UserInteractionInstrumentation with correct events', () => {
      const serviceName = randWord()

      initTracer(serviceName)

      expect(UserInteractionInstrumentation).toHaveBeenCalledWith({
        eventNames: ['click', 'submit', 'keydown']
      })
    })

    it('should log debug info when debug is true', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(vi.fn())
      const config = {
        ...mockTracerConfig(),
        debug: true
      }

      initTracer(config.serviceName, config)

      expect(consoleSpy).toHaveBeenCalledWith(
        'OpenTelemetry initialized successfully',
        {
          serviceName: config.serviceName,
          serviceVersion: config.serviceVersion,
          environment: config.environment,
          endpoint: config.endpoint
        }
      )

      consoleSpy.mockRestore()
    })

    it('should not log debug info when debug is false', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(vi.fn())
      const config = {
        ...mockTracerConfig(),
        debug: false
      }

      initTracer(config.serviceName, config)

      expect(consoleSpy).not.toHaveBeenCalled()

      consoleSpy.mockRestore()
    })

    it('should handle initialization errors', () => {
      const serviceName = randWord()
      const error = new Error('Initialization failed')
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(vi.fn())

      vi.mocked(WebTracerProvider).mockImplementationOnce(() => {
        throw error
      })

      expect(() => initTracer(serviceName)).toThrow(
        'OpenTelemetry initialization failed: Initialization failed'
      )
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Failed to initialize OpenTelemetry:',
        error
      )

      consoleErrorSpy.mockRestore()
    })

    it('should handle unknown errors', () => {
      const serviceName = randWord()
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(vi.fn())

      vi.mocked(WebTracerProvider).mockImplementationOnce(() => {
        throw 'String error'
      })

      expect(() => initTracer(serviceName)).toThrow(
        'OpenTelemetry initialization failed: Unknown error'
      )

      consoleErrorSpy.mockRestore()
    })
  })

  describe('getTracer', () => {
    it('should get tracer with default name', () => {
      getTracer()

      expect(trace.getTracer).toHaveBeenCalledWith('default')
    })

    it('should get tracer with custom name', () => {
      const tracerName = randWord()

      getTracer(tracerName)

      expect(trace.getTracer).toHaveBeenCalledWith(tracerName)
    })

    it('should return tracer instance', () => {
      const result = getTracer()

      expect(result).toBe(mockTracer)
    })
  })

  describe('createCustomSpan', () => {
    it('should create and execute span successfully', async () => {
      const spanName = randWord()
      const mockFn = vi.fn().mockResolvedValue(undefined)

      await createCustomSpan(spanName, mockFn)

      expect(mockTracer.startActiveSpan).toHaveBeenCalledWith(
        spanName,
        expect.any(Function)
      )
      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockSpan.end).toHaveBeenCalledTimes(1)
      expect(mockSpan.recordException).not.toHaveBeenCalled()
      expect(mockSpan.setStatus).not.toHaveBeenCalled()
    })

    it('should handle synchronous function', async () => {
      const spanName = randWord()
      const mockFn = vi.fn()

      await createCustomSpan(spanName, mockFn)

      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockSpan.end).toHaveBeenCalledTimes(1)
    })

    it('should handle asynchronous function', async () => {
      const spanName = randWord()
      const mockFn = vi.fn().mockResolvedValue('result')

      await createCustomSpan(spanName, mockFn)

      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockSpan.end).toHaveBeenCalledTimes(1)
    })

    it('should record exception and re-throw error', async () => {
      const spanName = randWord()
      const error = new Error('Test error')
      const mockFn = vi.fn().mockRejectedValue(error)

      await expect(createCustomSpan(spanName, mockFn)).rejects.toThrow(
        'Test error'
      )

      expect(mockSpan.recordException).toHaveBeenCalledWith(error)
      expect(mockSpan.setStatus).toHaveBeenCalledWith({
        code: 2,
        message: 'Test error'
      })
      expect(mockSpan.end).toHaveBeenCalledTimes(1)
    })

    it('should handle synchronous errors', async () => {
      const spanName = randWord()
      const error = new Error('Sync error')
      const mockFn = vi.fn().mockImplementation(() => {
        throw error
      })

      await expect(createCustomSpan(spanName, mockFn)).rejects.toThrow(
        'Sync error'
      )

      expect(mockSpan.recordException).toHaveBeenCalledWith(error)
      expect(mockSpan.setStatus).toHaveBeenCalledWith({
        code: 2,
        message: 'Sync error'
      })
      expect(mockSpan.end).toHaveBeenCalledTimes(1)
    })

    it('should always end span even if exception recording fails', async () => {
      const spanName = randWord()
      const error = new Error('Test error')
      const mockFn = vi.fn().mockRejectedValue(error)

      mockSpan.recordException.mockImplementation(() => {
        throw new Error('Recording failed')
      })

      await expect(createCustomSpan(spanName, mockFn)).rejects.toThrow(
        'Recording failed'
      )

      expect(mockSpan.end).toHaveBeenCalledTimes(1)
    })
  })
})
