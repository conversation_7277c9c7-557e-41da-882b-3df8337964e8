import { WebTracerProvider } from '@opentelemetry/sdk-trace-web'
import { resourceFromAttributes } from '@opentelemetry/resources'
import {
  ATTR_SERVICE_NAME,
  ATTR_SERVICE_VERSION
} from '@opentelemetry/semantic-conventions'
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base'
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http'
import { ZoneContextManager } from '@opentelemetry/context-zone'
import { registerInstrumentations } from '@opentelemetry/instrumentation'
import { FetchInstrumentation } from '@opentelemetry/instrumentation-fetch'
import { XMLHttpRequestInstrumentation } from '@opentelemetry/instrumentation-xml-http-request'
import { DocumentLoadInstrumentation } from '@opentelemetry/instrumentation-document-load'
import { UserInteractionInstrumentation } from '@opentelemetry/instrumentation-user-interaction'
import {
  CompositePropagator,
  W3CTraceContextPropagator,
  W3CBaggagePropagator
} from '@opentelemetry/core'
import { trace } from '@opentelemetry/api'

const ATTR_DEPLOYMENT_ENVIRONMENT = 'deployment.environment.name'

interface TracerConfig {
  serviceName: string
  serviceVersion?: string
  environment?: string
  endpoint?: string
  debug?: boolean
}

export function initTracer(
  serviceName: string,
  config?: Partial<TracerConfig>
): void {
  try {
    const tracerConfig: TracerConfig = {
      serviceName,
      serviceVersion: config?.serviceVersion || '1.0.0',
      environment: config?.environment || import.meta.env.MODE || 'development',
      endpoint: config?.endpoint,
      debug: config?.debug || false,
      ...config
    }

    const resource = resourceFromAttributes({
      [ATTR_SERVICE_NAME]: tracerConfig.serviceName,
      [ATTR_SERVICE_VERSION]: tracerConfig.serviceVersion,
      [ATTR_DEPLOYMENT_ENVIRONMENT]: tracerConfig.environment
    })

    const otlpExporter = new OTLPTraceExporter({
      url: tracerConfig.endpoint,
      headers: {}
    })

    const spanProcessor = new BatchSpanProcessor(otlpExporter, {
      maxExportBatchSize: 50,
      scheduledDelayMillis: 500,
      exportTimeoutMillis: 30000,
      maxQueueSize: 100
    })

    const provider = new WebTracerProvider({
      resource,
      spanProcessors: [spanProcessor]
    })

    provider.register({
      contextManager: new ZoneContextManager(),
      propagator: new CompositePropagator({
        propagators: [
          new W3CTraceContextPropagator(),
          new W3CBaggagePropagator()
        ]
      })
    })

    registerInstrumentations({
      instrumentations: [
        new DocumentLoadInstrumentation({
          ignoreNetworkEvents: false
        }),
        new FetchInstrumentation({
          propagateTraceHeaderCorsUrls: [/.*/],
          clearTimingResources: true,
          ignoreUrls: [
            /localhost:8090\/sockjs-node/,
            /_next\/static/,
            /favicon\.ico/,
            /\.hot-update\./
          ]
        }),
        new XMLHttpRequestInstrumentation({
          propagateTraceHeaderCorsUrls: [/.*/],
          ignoreUrls: [
            /localhost:8090\/sockjs-node/,
            /_next\/static/,
            /favicon\.ico/,
            /\.hot-update\./
          ]
        }),
        new UserInteractionInstrumentation({
          eventNames: ['click', 'submit', 'keydown']
        })
      ]
    })

    trace.setGlobalTracerProvider(provider)

    if (tracerConfig.debug) {
      console.log('OpenTelemetry initialized successfully', {
        serviceName: tracerConfig.serviceName,
        serviceVersion: tracerConfig.serviceVersion,
        environment: tracerConfig.environment,
        endpoint: tracerConfig.endpoint
      })
    }
  } catch (error) {
    console.error('Failed to initialize OpenTelemetry:', error)
    throw new Error(
      `OpenTelemetry initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    )
  }
}

export function getTracer(name?: string) {
  return trace.getTracer(name || 'default')
}

export function createCustomSpan(name: string, fn: () => void | Promise<void>) {
  const tracer = getTracer()
  return tracer.startActiveSpan(name, async (span) => {
    try {
      await fn()
    } catch (error) {
      span.recordException(error as Error)
      span.setStatus({ code: 2, message: (error as Error).message })
      throw error
    } finally {
      span.end()
    }
  })
}
