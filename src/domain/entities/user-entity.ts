export type UserEntity = {
  id: string
  email: string
  username: string
  avatar?: string
  isAdmin: boolean
}

export class User {
  static create = (user: UserEntity): User => new User(user)

  private constructor(private readonly user: UserEntity) {}

  get id(): string {
    return this.user.id
  }

  get email(): string {
    return this.user.email
  }

  get username(): string {
    return this.user.username
  }

  get avatar(): string | undefined {
    return this.user.avatar
  }

  get isAdmin(): boolean {
    return this.user.isAdmin
  }
}
