export interface Logger {
  log(entry: LogEntry): void
  setContext(key: string, value: any): void
  getContext(): Record<string, any>
}

export const LogLevel = {
  FATAL: 'FATAL',
  ERROR: 'ERROR',
  WARN: 'WARN',
  INFO: 'INFO',
  DEBUG: 'DEBUG',
  TRACE: 'TRACE'
} as const

export type LogLevel = (typeof LogLevel)[keyof typeof LogLevel]

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context: Record<string, any>
  service_name: string
}

export interface LogContext {
  [key: string]: any
}

export interface LogMetadata {
  timestamp: string
  source?: string
  correlationId?: string
  sessionId?: string
  userId?: string
}

export class AppLogger {
  private logger: Logger
  private serviceName: string
  private defaultContext: LogContext = {}

  constructor(logger: Logger, serviceName: string) {
    this.logger = logger
    this.serviceName = serviceName
  }

  setGlobalContext(context: LogContext): void {
    this.defaultContext = { ...this.defaultContext, ...context }
    Object.entries(context).forEach(([key, value]) => {
      this.logger.setContext(key, value)
    })
  }

  clearGlobalContext(): void {
    this.defaultContext = {}
  }

  private getStackTrace(): string | undefined {
    const stack = new Error().stack
    if (!stack) return undefined

    const lines = stack.split('\n')
    const callerLine = lines.find(
      (line) =>
        !line.includes('AppLogger') &&
        !line.includes('Object.log') &&
        line.includes('at ')
    )

    return callerLine?.trim()
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context: LogContext = {},
    source?: string
  ): LogEntry {
    const metadata: LogMetadata = {
      timestamp: new Date().toISOString(),
      source: source || this.getStackTrace(),
      ...this.defaultContext
    }

    return {
      timestamp: metadata.timestamp,
      level,
      message,
      context: {
        ...metadata,
        ...context
      },
      service_name: this.serviceName
    }
  }

  fatal(message: string, context: LogContext = {}, source?: string): void {
    const entry = this.createLogEntry(LogLevel.FATAL, message, context, source)
    this.logger.log(entry)
  }

  error(message: string, context: LogContext = {}, source?: string): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, source)
    this.logger.log(entry)
  }

  warn(message: string, context: LogContext = {}, source?: string): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, context, source)
    this.logger.log(entry)
  }

  info(message: string, context: LogContext = {}, source?: string): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, context, source)
    this.logger.log(entry)
  }

  debug(message: string, context: LogContext = {}, source?: string): void {
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context, source)
    this.logger.log(entry)
  }

  trace(message: string, context: LogContext = {}, source?: string): void {
    const entry = this.createLogEntry(LogLevel.TRACE, message, context, source)
    this.logger.log(entry)
  }

  log(
    level: LogLevel,
    message: string,
    context: LogContext = {},
    source?: string
  ): void {
    const entry = this.createLogEntry(level, message, context, source)
    this.logger.log(entry)
  }
}
