export type Either<E, S> = Failure<E, S> | Success<E, S>

export class Failure<E, S> {
  readonly error: E

  constructor(error: E) {
    this.error = error
  }

  isFailure(): this is Failure<E, S> {
    return true
  }

  isSuccess(): this is Success<E, S> {
    return false
  }
}

export class Success<E, S> {
  readonly value: S

  constructor(value: S) {
    this.value = value
  }

  isFailure(): this is Failure<E, S> {
    return false
  }

  isSuccess(): this is Success<E, S> {
    return true
  }
}

export const failure = <E, S>(e: E): Either<E, S> => {
  return new Failure<E, S>(e)
}

export const success = <E, S>(s: S): Either<E, S> => {
  return new Success<E, S>(s)
}
