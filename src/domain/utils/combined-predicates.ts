import { type Either, Failure, Success } from '~/domain/utils/either'

type Predicate = (value: any) => boolean

export type CombinedPredicated<E> = [Predicate, E][]

export const combinedPredicates = <E, S>({
  value,
  predicatePairs
}: {
  value: S
  predicatePairs: CombinedPredicated<E>
}): Either<E, S> => {
  for (const [verifier, error] of predicatePairs) {
    if (verifier(value)) {
      return new Failure(error)
    }
  }

  return new Success(value)
}
