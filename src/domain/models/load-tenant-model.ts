export interface LoadTenantModel {
  id: string
  name: string
  logoUrl: string
  url: string
  slug: string
  designTokens: DesignTokens
  appVersion: string
}

export interface DesignTokens {
  shape: Shape
  palette: Palette
  fontFaces: string
  typography: Typography
}

export interface Shape {
  borderRadius: number
  buttonRadius: number
  textAreaRadius: number
  calendarDayRadius: number
}

export interface Palette {
  info: Info
  text: Text
  error: Error
  border: Border
  states: States
  neutral: Neutral
  primary: Primary
  success: Success
  warning: Warning
  dividers: Dividers
  inverted: Inverted
  secondary: Secondary
  background: Background
  destructive: Destructive
}

export interface Info {
  dark: string
  main: string
  light: string
  contrastText: string
}

export interface Text {
  low: string
  high: string
  medium: string
  inverse: string
  primary: string
  authTitle: string
  secondary: string
  suitePrice: string
  footerMobile: string
  greetingText: string
  footerDesktop: string
  disclaimerIcon: string
  specialDatePrice: string
  extraPackagePageTitle: string
}

export interface Error {
  dark: string
  main: string
  light: string
  contrastText: string
}

export interface Border {
  periodButton: string
  periodButtonHover: string
}

export interface States {
  info: string
  error: string
  infoBg: string
  errorBg: string
  success: string
  warning: string
  successBg: string
  warningBg: string
}

export interface Neutral {
  '10': string
  '20': string
  '30': string
  '40': string
  '50': string
  '60': string
  '70': string
  '80': string
  '90': string
  '95': string
  '99': string
  black: string
  white: string
}

export interface Primary {
  main: string
  hover: string
  light: string
  active: string
  default: string
  hover10: string
  visited: string
  active20: string
  disabled: string
  inverted: string
}

export interface Success {
  dark: string
  main: string
  light: string
  contrastText: string
}

export interface Warning {
  dark: string
  main: string
  light: string
  contrastText: string
}

export interface Dividers {
  mobile: string
  desktop: string
  menuFilter: string
}

export interface Inverted {
  main: string
  hover: string
  active: string
  default: string
  hover10: string
  visited: string
  active20: string
  disabled: string
}

export interface Secondary {
  dark: string
  main: string
  hover: string
  light: string
  active: string
  hover4: string
  default: string
  hover10: string
  visited: string
  active20: string
  disabled: string
  inverted: string
  contrastText: string
}

export interface Background {
  dark: string
  light: string
  paper: string
  button: string
  darker: string
  darkest: string
  default: string
  lighter: string
  suiteTag: string
  tableHead: string
  menuFilter: string
  amenityIcon: string
  buttonHover: string
  calendarDay: string
  switchTrack: string
  checkboxIcon: string
  footerMobile: string
  searchButton: string
  footerDesktop: string
  tableHeadDark: string
  scrollIntoView: string
  suiteTagBanner: string
  buttonSecondary: string
  calendarCurrentDay: string
  buttonHoverSecondary: string
}

export interface Destructive {
  main: string
  hover: string
  active: string
  default: string
  hover10: string
  visited: string
  active20: string
  disabled: string
  inverted: string
}

export interface Typography {
  fontFamily: string
  suiteTagFont: string
  secondaryFontFamily: string
  suiteNameLineHeight: number
  suiteTableDaysWeight: number
  suiteTablePriceWeight: number
}
