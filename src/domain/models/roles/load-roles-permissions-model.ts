export type PermissionsGroup = Group[]

export interface Group {
  id: string
  name: string
  type: 'permission' | 'group'
  children: Children[]
  open?: boolean
  checked?: boolean
}

export interface Children {
  id: string
  name: string
  type: 'permission' | 'group'
  children: Permission[]
  open?: boolean
  checked?: boolean
}

export interface Permission {
  id: string
  name: string
  action: string
  type: 'permission' | 'group'
  isActive: boolean
  open?: boolean
  checked?: boolean
}
