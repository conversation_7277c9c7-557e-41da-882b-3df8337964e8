import {
  type DomainException,
  type DomainExceptionParams
} from './domain-exception'

export class isConflictException implements DomainException {
  type: string
  title: string
  message: string
  error?: any

  constructor({
    error,
    message,
    title,
    type = 'isConflictException'
  }: DomainExceptionParams) {
    this.type = type
    this.title = title
    this.message = message
    this.error = error
  }
}
