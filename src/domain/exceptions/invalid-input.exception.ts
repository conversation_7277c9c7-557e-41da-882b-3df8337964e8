import {
  type DomainException,
  type DomainExceptionParams
} from './domain-exception'

export class InvalidInputException implements DomainException {
  type: string
  title: string
  message: string
  error?: any

  constructor({
    error,
    message = 'Invalid input',
    title,
    type = 'InvalidInputException'
  }: DomainExceptionParams) {
    this.type = type
    this.title = title
    this.message = message
    this.error = error
  }
}
