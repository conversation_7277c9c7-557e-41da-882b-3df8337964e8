import {
  type DomainException,
  type DomainExceptionParams
} from './domain-exception'

export class BadRequestException implements DomainException {
  type: string
  title: string
  message: string
  error?: any

  constructor({
    error,
    message,
    title,
    type = 'BadRequestException'
  }: DomainExceptionParams) {
    this.type = type
    this.title = title
    this.message = message
    this.error = error
  }
}
