import {
  type DomainException,
  type DomainExceptionParams
} from './domain-exception'

export class NotFoundException implements DomainException {
  type: string
  title: string
  message: string
  error?: any

  constructor({
    error,
    message = 'Not found',
    title,
    type = 'NotFoundException'
  }: DomainExceptionParams) {
    this.type = type
    this.title = title
    this.message = message
    this.error = error
  }
}
