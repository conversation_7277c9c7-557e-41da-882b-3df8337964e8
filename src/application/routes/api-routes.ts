import { type HttpMethod } from '~/infra/http/http-client'
import { API_URL } from '~/main/env'

export const API_ROUTES_NAMES = {
  SIGN_IN: 'sign-in',
  GET_USER: 'get-user',
  GET_COURSES: 'get-courses',
  GET_COURSES_LANGS: 'get-courses-langs',
  GET_CATEGORY_COURSES: 'get-category-courses',
  GET_COURSE_DETAILS: 'get-course-details',
  GET_CATEGORIES_LIST: 'get-categories-list',
  GET_CATEGORIES_PAGINATED: 'get-categories-paginated',
  SEARCH_COURSES: 'search-courses'
} as const

export type ApiRouteConfig<T extends object = {}> = (params?: T) => {
  method: HttpMethod
  url: string
}

type ParametrizedApiRouteConfig = ApiRouteConfig<Record<string, string>>
type NonParametrizedApiRouteConfig = ApiRouteConfig<{}>

type GeneralApiRouteConfig = ParametrizedApiRouteConfig &
  NonParametrizedApiRouteConfig

export type ApiRoutesNames =
  (typeof API_ROUTES_NAMES)[keyof typeof API_ROUTES_NAMES]

export const apiRoutes = new Map<ApiRoutesNames, GeneralApiRouteConfig>([
  [
    API_ROUTES_NAMES.SIGN_IN,
    () => ({
      method: 'POST',
      url: '/account/auth'
    })
  ],
  [
    API_ROUTES_NAMES.GET_USER,
    () => ({
      method: 'GET',
      url: '/user'
    })
  ],
  [
    API_ROUTES_NAMES.GET_CATEGORIES_PAGINATED,
    () => ({
      method: 'GET',
      url: '/categories'
    })
  ],
  [
    API_ROUTES_NAMES.GET_COURSES,
    () => ({
      method: 'GET',
      url: '/courses'
    })
  ],
  [
    API_ROUTES_NAMES.GET_COURSES_LANGS,
    () => ({
      method: 'GET',
      url: '/languages'
    })
  ],
  [
    API_ROUTES_NAMES.GET_CATEGORY_COURSES,
    ((params: { id: string }) => ({
      method: 'GET',
      url: `/courses/category/${params.id}`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.GET_COURSE_DETAILS,
    () => ({
      method: 'GET',
      url: '/courses/:id/details'
    })
  ],
  [
    API_ROUTES_NAMES.GET_CATEGORIES_LIST,
    () => ({
      method: 'GET',
      url: '/categories/list'
    })
  ],
  [
    API_ROUTES_NAMES.SEARCH_COURSES,
    () => ({
      method: 'GET',
      url: '/courses/search'
    })
  ]
])

export const getApiRoute = <T extends object = {}>(
  routeName: ApiRoutesNames
) => {
  const routeConfig = apiRoutes.get(routeName)

  if (!routeConfig) {
    throw new Error(`Route ${routeName} not found`)
  }

  return (params?: T) => {
    const route = routeConfig(params)
    const apiUrl = API_URL || ''
    return {
      ...route,
      url: `${apiUrl}${route.url}`
    }
  }
}
