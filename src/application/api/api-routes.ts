import { type HttpMethod } from '~/infra/http/http-client'
import { API_URL } from '~/main/env'

export const API_ROUTES_NAMES = {
  LOGIN: 'account/login',
  LOGOUT: 'account/logout',
  FORGOT_PASSWORD: 'account/forgot-password',
  REGISTER: 'tenant/register',
  REGISTER_USER: 'register-user',
  SYSTEM_CONFIG: 'system-config',
  PROFILE: 'profile',
  TENANT: 'tenant',
  CONFIRM_ACCOUNT: 'account/confirm-account',
  RESEND_ACCOUNT_CONFIRMATION: 'account/resend-account-confirmation',
  VALIDATE_SECURITY_TOKEN: 'account/validate-security-token',
  RESET_PASSWORD: 'account/reset-password',
  ACCOUNT_ME: 'account/me',
  ROLES: 'role',
  ROLES_PERMISSIONS: 'load-roles',
  ROLES_PERMISSIONS_UPDATE: 'role-update',
  LOAD_ROLE: 'load-role',
  UPDATE_ROL<PERSON>: 'update-role',
  CREATE_ROLE: 'create-role',
  LOAD_ROLE_USERS: 'load-role-users',
  DELETE_ROLE_USER: 'delete-role-user',
  ADD_ROLE_USER: 'add-role-user',
  LOAD_ROLE_USERS_LINKED: 'load-role-users-linked',
  LOAD_ROLE_USERS_UNLINKED: 'load-role-users-unlinked'
} as const

export type ApiRouteConfig<T extends object = {}> = keyof T extends never
  ? () => {
      method: HttpMethod
      url: string
    }
  : (params: T) => {
      method: HttpMethod
      url: string
    }

type ParametrizedApiRouteConfig = ApiRouteConfig<Record<string, string>>
type NonParametrizedApiRouteConfig = ApiRouteConfig<Record<string, string>>

type GeneralApiRouteConfig = ParametrizedApiRouteConfig &
  NonParametrizedApiRouteConfig

export type ApiRoutesNames =
  (typeof API_ROUTES_NAMES)[keyof typeof API_ROUTES_NAMES]

export const apiRoutes = new Map<ApiRoutesNames, GeneralApiRouteConfig>([
  [
    API_ROUTES_NAMES.TENANT,
    () => ({
      method: 'GET',
      url: '/tenant'
    })
  ],
  [
    API_ROUTES_NAMES.LOGIN,
    () => ({
      method: 'POST',
      url: '/account/login'
    })
  ],
  [
    API_ROUTES_NAMES.CONFIRM_ACCOUNT,
    () => ({
      method: 'POST',
      url: '/account/confirm-account'
    })
  ],
  [
    API_ROUTES_NAMES.RESEND_ACCOUNT_CONFIRMATION,
    () => ({
      method: 'POST',
      url: '/account/resend-account-confirmation'
    })
  ],
  [
    API_ROUTES_NAMES.VALIDATE_SECURITY_TOKEN,
    () => ({
      method: 'POST',
      url: '/account/validate-security-token'
    })
  ],
  [
    API_ROUTES_NAMES.LOGOUT,
    () => ({
      method: 'POST',
      url: '/account/logout'
    })
  ],
  [
    API_ROUTES_NAMES.REGISTER,
    () => ({
      method: 'POST',
      url: '/tenant/register'
    })
  ],
  [
    API_ROUTES_NAMES.FORGOT_PASSWORD,
    () => ({
      method: 'POST',
      url: '/account/forgot-password'
    })
  ],
  [
    API_ROUTES_NAMES.RESET_PASSWORD,
    () => ({
      method: 'POST',
      url: '/account/reset-password'
    })
  ],
  [
    API_ROUTES_NAMES.REGISTER_USER,
    () => ({
      method: 'POST',
      url: '/register-user'
    })
  ],
  [
    API_ROUTES_NAMES.SYSTEM_CONFIG,
    () => ({
      method: 'GET',
      url: '/system-config'
    })
  ],
  [
    API_ROUTES_NAMES.ACCOUNT_ME,
    () => ({
      method: 'GET',
      url: '/account/me'
    })
  ],
  [
    API_ROUTES_NAMES.PROFILE,
    ((params: { id: string }) => ({
      method: 'GET',
      url: `/profile/${params?.id}`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.ROLES,
    (() => ({
      method: 'GET',
      url: `/role`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.ROLES_PERMISSIONS,
    ((params: { roleId: string }) => ({
      method: 'GET',
      url: `/role/${params?.roleId}/permissions`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.ROLES_PERMISSIONS_UPDATE,
    ((params: { roleId: string }) => ({
      method: 'PATCH',
      url: `/role/${params?.roleId}/permissions`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.LOAD_ROLE,
    ((params: { roleId: string }) => ({
      method: 'GET',
      url: `/role/${params?.roleId}`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.UPDATE_ROLE,
    ((params: { roleId: string }) => ({
      method: 'PATCH',
      url: `/role/${params?.roleId}`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.CREATE_ROLE,
    (() => ({
      method: 'POST',
      url: `/role`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.LOAD_ROLE_USERS,
    ((params: { roleId: string }) => ({
      method: 'GET',
      url: `/role/${params?.roleId}/users`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.DELETE_ROLE_USER,
    ((params: { roleId: string; userId: string }) => ({
      method: 'DELETE',
      url: `/role/${params?.roleId}/users/${params?.userId}`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.ADD_ROLE_USER,
    ((params: { roleId: string }) => ({
      method: 'POST',
      url: `/role/${params?.roleId}/users`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.LOAD_ROLE_USERS_LINKED,
    ((params: { roleId: string }) => ({
      method: 'GET',
      url: `/role/${params?.roleId}/users/linked`
    })) as ParametrizedApiRouteConfig
  ],
  [
    API_ROUTES_NAMES.LOAD_ROLE_USERS_UNLINKED,
    ((params: { roleId: string }) => ({
      method: 'GET',
      url: `/role/${params?.roleId}/users/unlinked`
    })) as ParametrizedApiRouteConfig
  ]
])

export const getApiRoute = <T extends Record<string, string> = {}>(
  routeName: ApiRoutesNames
) => {
  const routeConfig = apiRoutes.get(routeName)

  if (!routeConfig) {
    throw new Error(`Route ${routeName} not found`)
  }

  return (params?: T) => {
    const route = routeConfig(params!)
    const apiUrl = API_URL || ''

    return {
      ...route,
      url: `${apiUrl}${route.url}`
    }
  }
}
