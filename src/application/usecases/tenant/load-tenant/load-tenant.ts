import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import type { LoadTenantModel } from '~/domain/models/load-tenant-model'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class LoadTenant
  implements UseCase<LoadTenant.Response, LoadTenant.Params>
{
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute(
    params: LoadTenant.Params
  ): UseCase.Response<LoadTenant.Response> {
    const route = this.route()
    const responseOrError = await this.httpClient.request<
      LoadTenant.Response,
      LoadTenant.Params
    >({
      authorized: false,
      method: route.method,
      url: route.url,
      body: params
    })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace LoadTenant {
  export type Params = void
  export type Response = LoadTenantModel
}
