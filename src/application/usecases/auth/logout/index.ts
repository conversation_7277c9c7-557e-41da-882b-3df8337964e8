import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { AccountLogout } from './logout'

export const makeAccountLogoutService = (): UseCase<
  AccountLogout.Response,
  AccountLogout.Params
> => new AccountLogout(makeTenantHttpClient(), getApiRoute('account/logout'))

export type { AccountLogout }
