import { type HandledHttpClient } from '~/infra/http/http-client'
import { AccountLogout } from './logout'
import { getApiRoute } from '~/application/api/api-routes'
import { makeHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'
import { failure } from '~/domain/utils/either'

describe('Account Logout', () => {
  serverStarter()

  it('Should return success() when call service', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/logout')
    const logoutService = new AccountLogout(httpClient, route)

    const result =
      (await logoutService.execute()) as TestSuccessResult<AccountLogout.Response>
    expect(result.isSuccess()).toBe(true)
  })

  it('Should return failure() on error', async () => {
    const exceptionError = new UnexpectedException({
      error: '',
      message: 'Unexpected error',
      title: 'Unexpected error',
      type: 'UnexpectedException'
    })

    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/logout')
    const logoutService = new AccountLogout(httpClient, route)

    vi.spyOn(httpClient, 'request').mockResolvedValueOnce(
      failure(exceptionError)
    )

    const response =
      (await logoutService.execute()) as TestFailureResult<AccountLogout.Response>
    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(exceptionError)
  })
})
