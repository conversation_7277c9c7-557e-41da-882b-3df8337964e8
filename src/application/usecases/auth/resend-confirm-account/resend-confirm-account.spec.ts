import { type HandledHttpClient } from '~/infra/http/http-client'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import { ResendConfirmAccount } from './resend-confirm-account'
import { getApiRoute } from '~/application/api/api-routes'
import { makeHttpClient } from '~/infra'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'

describe('ResendConfirmAccount', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/resend-account-confirmation')
    const resendConfirmAccount = new ResendConfirmAccount(httpClient, route)

    const params = {
      email: '<EMAIL>',
      tenantId: 'faker_tenant_id'
    }

    const result = (await resendConfirmAccount.execute(
      params
    )) as TestSuccessResult<ResendConfirmAccount.Response>
    expect(result.isSuccess()).toBe(true)
  })

  it('should return failure when invalid credentials are provided', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/confirm-account')
    const resendConfirmAccount = new ResendConfirmAccount(httpClient, route)

    const params = {
      email: 'invalid_faker_email.com',
      tenantId: 'faker_tenant_id'
    }

    try {
      await resendConfirmAccount.execute(params)
    } catch (error) {
      const response = error as TestFailureResult<ResendConfirmAccount.Response>

      expect(response.isFailure()).toBe(true)
    }
  })
})
