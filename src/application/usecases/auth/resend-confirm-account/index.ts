import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { ResendConfirmAccount } from './resend-confirm-account'

export const makeResendConfirmAccountService = (): UseCase<
  ResendConfirmAccount.Response,
  ResendConfirmAccount.Params
> =>
  new ResendConfirmAccount(
    makeTenantHttpClient(),
    getApiRoute('account/resend-account-confirmation')
  )

export type { ResendConfirmAccount }
