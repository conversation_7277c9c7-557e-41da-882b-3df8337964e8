import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class ValidateSecurityToken
  implements UseCase<ValidateSecurityToken.Response>
{
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute({
    ...params
  }: ValidateSecurityToken.Params): UseCase.Response<ValidateSecurityToken.Response> {
    const route = this.route()

    const responseOrError =
      await this.httpClient.request<ValidateSecurityToken.Response>({
        authorized: false,
        method: route.method,
        url: route.url,
        body: params
      })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace ValidateSecurityToken {
  export type Params = {
    securityToken: string
  }
  export type Response = void
}
