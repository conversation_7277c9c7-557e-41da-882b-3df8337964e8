import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { ValidateSecurityToken } from './validate-security-token'

export const makeValidateSecurityTokenService = (): UseCase<
  ValidateSecurityToken.Response,
  ValidateSecurityToken.Params
> =>
  new ValidateSecurityToken(
    makeTenantHttpClient(),
    getApiRoute('account/validate-security-token')
  )

export type { ValidateSecurityToken }
