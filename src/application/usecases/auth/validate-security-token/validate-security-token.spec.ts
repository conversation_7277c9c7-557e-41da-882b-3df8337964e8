import { type HandledHttpClient } from '~/infra/http/http-client'
import { ValidateSecurityToken } from './validate-security-token'
import { getApiRoute } from '~/application/api/api-routes'
import { makeHttpClient } from '~/infra'
import { AccessDeniedException } from '~/domain/exceptions/access-denied.exception'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'

describe('ValidateSecurityToken', () => {
  serverStarter()

  it('Should return success() with correct securityToken', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/validate-security-token')
    const validateSecurityToken = new ValidateSecurityToken(httpClient, route)

    const params = {
      securityToken: 'valid_security_token',
      tenantId: 'faker_tenant_id'
    }

    const result = (await validateSecurityToken.execute(
      params
    )) as TestSuccessResult<ValidateSecurityToken.Response>
    expect(result.isSuccess()).toBe(true)
  })

  it('should return failure when invalid securityToken are provided', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/validate-security-token')
    const validateSecurityToken = new ValidateSecurityToken(httpClient, route)

    const params = {
      securityToken: 'invalid_security_token',
      tenantId: 'faker_tenant_id'
    }

    const result = (await validateSecurityToken.execute(
      params
    )) as TestFailureResult<ValidateSecurityToken.Response>
    expect(result.isFailure()).toBe(true)
  })

  it('should return failure when securityToken is expired', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/validate-security-token')
    const validateSecurityToken = new ValidateSecurityToken(httpClient, route)

    const params = {
      securityToken: 'expired_security_token',
      tenantId: 'faker_tenant_id'
    }

    const result = (await validateSecurityToken.execute(
      params
    )) as TestFailureResult<ValidateSecurityToken.Response>
    expect(result.isFailure()).toBe(true)

    expect(result.error).toEqual(
      new AccessDeniedException({
        error: {
          errors: 'Token is expired'
        },
        message: 'Unauthorized',
        title: 'Unauthorized',
        type: 'UnauthorizedException'
      })
    )
  })
})
