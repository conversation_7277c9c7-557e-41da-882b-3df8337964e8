import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { ForgotPassword } from './forgot-password'

export const makeForgotPasswordService = (): UseCase<
  ForgotPassword.Response,
  ForgotPassword.Params
> =>
  new ForgotPassword(
    makeTenantHttpClient(),
    getApiRoute('account/forgot-password')
  )

export type { ForgotPassword }
