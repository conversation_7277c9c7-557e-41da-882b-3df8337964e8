import { success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'
import { makeHttpClient } from '~/infra'
import { getApiRoute } from '~/application/api/api-routes'
import { ForgotPassword } from './forgot-password'
import { NotFoundException } from '~/domain/exceptions/not-found.exception'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestFailureResult,
  TestSuccessResult
} from '~/main/config/tests/types/result-types'

const makeSut = () => {
  const httpClient: HandledHttpClient = makeHttpClient()
  const route = getApiRoute('account/forgot-password')
  const passwordReset = new ForgotPassword(httpClient, route)

  return {
    passwordReset,
    url: route().url,
    httpClient
  }
}

describe('PasswordReset', () => {
  serverStarter()

  it('Should return error() if RequestResponse fails', async () => {
    const { passwordReset } = makeSut()

    const params = {
      email: 'invalid_email.com',
      tenantId: 'fake_tenant_id'
    }

    const response = (await passwordReset.execute(
      params
    )) as TestFailureResult<ForgotPassword.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(
      new NotFoundException({
        error: {
          message: 'Email not found.',
          success: false
        },
        type: 'NotFoundException',
        title: 'Not found',
        message: 'Not found'
      })
    )
  })

  it('Should return success() with correct values', async () => {
    const { passwordReset } = makeSut()

    const params = {
      email: '<EMAIL>',
      tenantId: 'fake_tenant_id'
    }

    const response = (await passwordReset.execute(
      params
    )) as TestSuccessResult<ForgotPassword.Response>

    expect(response.isSuccess()).toBe(true)
    expect(response).toEqual(
      success({
        message: 'New password sent, check your email.',
        success: true
      })
    )
  })
})
