import { type HandledHttpClient } from '~/infra/http/http-client'
import { ConfirmAccount } from './confirm-account'
import { getApiRoute } from '~/application/api/api-routes'
import { makeHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'

describe('ConfirmAccount', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/confirm-account')
    const emailLogin = new ConfirmAccount(httpClient, route)

    const params = {
      securityToken: 'faker_valid_security_token',
      tenantId: 'faker_tenant_id'
    }

    const result = (await emailLogin.execute(
      params
    )) as TestSuccessResult<ConfirmAccount.Response>
    expect(result.isSuccess()).toBe(true)
  })

  it('should return failure when invalid credentials are provided', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/confirm-account')
    const emailLogin = new ConfirmAccount(httpClient, route)

    const params = {
      securityToken: 'faker_invalid_security_token',
      tenantId: 'faker_tenant_id'
    }

    const result = (await emailLogin.execute(
      params
    )) as TestFailureResult<ConfirmAccount.Response>
    expect(result.isFailure()).toBe(true)
  })
})
