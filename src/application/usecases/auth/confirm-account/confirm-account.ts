import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class ConfirmAccount implements UseCase<ConfirmAccount.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute({
    ...params
  }: ConfirmAccount.Params): UseCase.Response<ConfirmAccount.Response> {
    const route = this.route()

    const responseOrError =
      await this.httpClient.request<ConfirmAccount.Response>({
        authorized: false,
        method: route.method,
        url: route.url,
        body: params
      })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace ConfirmAccount {
  export type Params = {
    securityToken: string
  }
  export type Response = void
}
