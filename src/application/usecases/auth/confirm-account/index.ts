import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { ConfirmAccount } from './confirm-account'

export const makeConfirmAccountService = (): UseCase<
  ConfirmAccount.Response,
  ConfirmAccount.Params
> =>
  new ConfirmAccount(
    makeTenantHttpClient(),
    getApiRoute('account/confirm-account')
  )

export type { ConfirmAccount }
