import { type HandledHttpClient } from '~/infra/http/http-client'
import { ResetPassword } from './reset-password'
import { getApiRoute } from '~/application/api/api-routes'
import { makeHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { success } from '~/domain/utils/either'

describe('ResetPassword', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/reset-password')
    const ResetPasswordService = new ResetPassword(httpClient, route)

    const params = {
      password: 'qwe123QWE@',
      securityToken: 'valid_token'
    }

    const result = (await ResetPasswordService.execute(
      params
    )) as Test<PERSON><PERSON><PERSON>R<PERSON>ult<ResetPassword.Response>

    expect(result.isSuccess()).toBe(true)
    expect(result).toEqual(
      success({
        message: 'Successfully Reset Password',
        success: true
      })
    )
  })

  it('should return failure when passed incorrect params', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/reset-password')
    const ResetPasswordService = new ResetPassword(httpClient, route)

    const params = {
      password: '123456',
      securityToken: 'invalid_token'
    }

    const result = (await ResetPasswordService.execute(
      params
    )) as TestFailureResult<ResetPassword.Response>

    expect(result.isFailure()).toBe(true)
  })
})
