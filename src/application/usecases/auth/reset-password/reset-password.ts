import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class ResetPassword implements UseCase<ResetPassword.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute({
    ...params
  }: ResetPassword.Params): UseCase.Response<ResetPassword.Response> {
    const route = this.route()
    const responseOrError =
      await this.httpClient.request<ResetPassword.Response>({
        authorized: false,
        method: route.method,
        url: route.url,
        body: params
      })

    if (responseOrError.isFailure()) {
      return failure(responseOrError.error)
    }

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace ResetPassword {
  export type Params = {
    securityToken: string
    password: string
  }

  export type Response = void
}
