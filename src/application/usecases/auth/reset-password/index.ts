import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { ResetPassword } from './reset-password'

export const makeResetPasswordService = (): UseCase<
  ResetPassword.Response,
  ResetPassword.Params
> =>
  new ResetPassword(
    makeTenantHttpClient(),
    getApiRoute('account/reset-password')
  )

export type { ResetPassword }
