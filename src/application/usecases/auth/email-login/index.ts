import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { EmailLogin } from './email-login'

export const makeEmailLoginService = (): UseCase<
  EmailLogin.Response,
  EmailLogin.Params
> => new EmailLogin(makeTenantHttpClient(), getApiRoute('account/login'))

export type { EmailLogin }
