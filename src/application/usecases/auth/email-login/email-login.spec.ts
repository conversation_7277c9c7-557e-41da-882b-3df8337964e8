import { type HandledHttpClient } from '~/infra/http/http-client'
import { EmailLogin } from './email-login'
import { getApiRoute } from '~/application/api/api-routes'
import { makeHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import { AccessDeniedException } from '~/domain/exceptions/access-denied.exception'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'

describe('EmailLogin', () => {
  serverStarter()

  it('should return success when valid credentials are provided with HandledHttpClient', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/login')
    const emailLogin = new EmailLogin(httpClient, route)

    const params = {
      email: '<EMAIL>',
      password: 'Softo123@',
      tenantId: 'fake_tenant_id'
    }
    const result = (await emailLogin.execute(
      params
    )) as TestSuccessResult<EmailLogin.Response>

    expect(result.isSuccess()).toBe(true)
    expect(result.value).toEqual({
      accessToken: 'valid-token',
      refreshToken: 'valid-refresh-token'
    })
  })

  it('should return failure when invalid credentials are provided', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/login')
    const emailLogin = new EmailLogin(httpClient, route)

    const params = {
      email: 'invalid.com',
      password: 'invalidPassword',
      tenantId: 'fake_tenant_id'
    }

    const result = (await emailLogin.execute(
      params
    )) as TestFailureResult<EmailLogin.Response>

    expect(result.isFailure()).toBe(true)
    expect(result.error).toEqual(
      new AccessDeniedException({
        error: {
          message: 'Invalid credentials',
          success: false
        },
        message: 'Forbidden',
        title: 'Forbidden',
        type: 'ForbiddenException'
      })
    )
  })
})
