import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { LOCALSTORAGE_KEYS } from '~/domain/entities/local-storage-keys'
import { type LoginForm } from '~/domain/models'
import { failure, success } from '~/domain/utils/either'
import { cacheStorage } from '~/infra'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class EmailLogin
  implements UseCase<EmailLogin.Response, EmailLogin.Params>
{
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute({
    ...params
  }: EmailLogin.Params): UseCase.Response<EmailLogin.Response> {
    const route = this.route()
    const responseOrError = await this.httpClient.request<
      EmailLogin.Response,
      Omit<EmailLogin.Params, 'tenantId'>
    >({
      authorized: false,
      method: route.method,
      url: route.url,
      body: params
    })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    cacheStorage.set(
      LOCALSTORAGE_KEYS.USER_AUTHENTICATED,
      JSON.stringify(response)
    )

    return success(response)
  }
}

export namespace EmailLogin {
  export type Params = LoginForm

  export type Response = {
    accessToken: string
    refreshToken: string
    email: string
    id: string
  }
}
