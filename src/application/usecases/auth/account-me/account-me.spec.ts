import { type HandledHttpClient } from '~/infra/http/http-client'
import { AccountMe } from './account-me'
import { getApiRoute } from '~/application/api/api-routes'
import { makeHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'
import { failure } from '~/domain/utils/either'

describe('AccountMe', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/me')
    const accountMe = new AccountMe(httpClient, route)

    const result =
      (await accountMe.execute()) as TestSuccessResult<AccountMe.Response>
    expect(result.isSuccess()).toBe(true)
  })

  it('Should return failure() on error', async () => {
    const exceptionError = new UnexpectedException({
      error: '',
      message: 'Unexpected error',
      title: 'Unexpected error',
      type: 'UnexpectedException'
    })

    const httpClient: HandledHttpClient = makeHttpClient()
    const route = getApiRoute('account/me')
    const accountMe = new AccountMe(httpClient, route)

    vi.spyOn(httpClient, 'request').mockResolvedValueOnce(
      failure(exceptionError)
    )

    const response =
      (await accountMe.execute()) as TestFailureResult<AccountMe.Response>
    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(exceptionError)
  })
})
