import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import type { LoadAccountMeModel } from '~/domain/models/load-account-me-model'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class AccountMe implements UseCase<AccountMe.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute(): UseCase.Response<AccountMe.Response> {
    const route = this.route()

    const responseOrError = await this.httpClient.request<AccountMe.Response>({
      authorized: false,
      method: route.method,
      url: route.url
    })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace AccountMe {
  export type Params = void
  export type Response = LoadAccountMeModel
}
