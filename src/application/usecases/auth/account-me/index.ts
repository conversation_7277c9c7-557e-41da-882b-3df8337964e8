import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { AccountMe } from './account-me'

export const makeLoadAccountMeService = (): UseCase<
  AccountMe.Response,
  AccountMe.Params
> => new AccountMe(makeTenantHttpClient(), getApiRoute('account/me'))

export type { AccountMe }
