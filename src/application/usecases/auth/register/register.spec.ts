import { success } from '~/domain/utils/either'
import { getApiRoute } from '~/application/api/api-routes'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import { Register } from './register'
import type { HandledHttpClient } from '~/infra/http/http-client'
import { makeHttpClient } from '~/infra'
import { BadRequestException } from '~/domain/exceptions/bad-request.exception'
import type {
  TestFailureResult,
  TestSuccessResult
} from '~/main/config/tests/types/result-types'

const makeSut = () => {
  const httpClient: HandledHttpClient = makeHttpClient()
  const route = getApiRoute('tenant/register')
  const register = new Register(httpClient, route)

  return {
    register,
    url: route().url,
    httpClient
  }
}

describe('Register', () => {
  serverStarter()

  it('Should return error() if RequestResponse fails', async () => {
    const { register } = makeSut()

    const params = {
      admin: {
        email: 'invalidemail.com',
        firstName: '<PERSON>',
        lastName: 'Doe',
        password: '@Senha123',
        language: 'en-US',
        timezone: 'America/Sao_Paulo'
      },
      tenant: {
        name: 'Sof.to',
        slug: 'softo'
      }
    }

    const response = (await register.execute(
      params
    )) as TestFailureResult<Register.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(
      new BadRequestException({
        error: {
          message: 'Invalid input data',
          success: false
        },
        type: 'BadRequestException',
        title: 'Bad request',
        message: 'Bad request'
      })
    )
  })

  it('Should return success() with correct values', async () => {
    const { register } = makeSut()

    const params = {
      admin: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: '@Senha123',
        language: 'en-US',
        timezone: 'America/Sao_Paulo'
      },
      tenant: {
        name: 'Sof.to',
        slug: 'softo'
      }
    }

    const response = (await register.execute(
      params
    )) as TestSuccessResult<Register.Response>

    expect(response.isSuccess()).toBe(true)
    expect(response).toEqual(
      success({
        message: 'Successfully registered user',
        success: true
      })
    )
  })
})
