import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import type { TenantRegisterModel } from '~/domain/models/tenant-register-model'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class Register implements UseCase<Register.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute(params: Register.Params): UseCase.Response<Register.Response> {
    const route = this.route()
    const responseOrError = await this.httpClient.request<Register.Response>({
      authorized: false,
      method: route.method,
      url: route.url,
      body: params
    })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace Register {
  export type Params = TenantRegisterModel
  export type Response = void
}
