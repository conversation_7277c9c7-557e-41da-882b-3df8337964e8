import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { type User } from '~/domain/entities/user-entity'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class GetUserProfile implements UseCase<GetUserProfile.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig<{
      id: string
    }>
  ) {}

  async execute({
    id
  }: GetUserProfile.Params): UseCase.Response<GetUserProfile.Response> {
    const route = this.route({ id: id })

    const responseOrError =
      await this.httpClient.request<GetUserProfile.Response>({
        authorized: true,
        method: route.method,
        url: route.url
      })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace GetUserProfile {
  export type Response = User
  export type Params = {
    id: string
  }
}
