import { GetUserProfile } from './get-user-profile'
import type { HandledHttpClient } from '~/infra/http/http-client'
import { makeHttpClient } from '~/infra'
import { getApiRoute } from '~/application/api/api-routes'
import { NotFoundException } from '~/domain/exceptions/not-found.exception'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'

const makeSut = () => {
  const params = { id: '7d330a09-c159-48b4-917f-d2d5dba6f063' }
  const httpClient: HandledHttpClient = makeHttpClient()
  const route = getApiRoute('profile')
  const getUserProfile = new GetUserProfile(httpClient, route)

  return {
    getUserProfile,
    url: route(params).url,
    httpClient
  }
}

describe('GetUserProfile', () => {
  serverStarter()

  it('Should return error() if RequestResponse fails', async () => {
    const { getUserProfile } = makeSut()

    // invalid uuid profile
    const response = (await getUserProfile.execute({
      id: '7d330a09-c159-48b4-917f-999999999'
    })) as TestFailureResult<GetUserProfile.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(
      new NotFoundException({
        error: {
          success: false,
          message: 'User profile not found'
        },
        type: 'NotFoundException',
        title: 'Not found',
        message: 'Not found'
      })
    )
  })

  it('Should return success() with correct values', async () => {
    const { getUserProfile } = makeSut()

    const response = (await getUserProfile.execute({
      id: '7d330a09-c159-48b4-917f-d2d5dba6f063'
    })) as TestSuccessResult<GetUserProfile.Response>

    expect(response.isSuccess()).toBe(true)
  })
})
