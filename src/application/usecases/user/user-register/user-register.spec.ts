import { success } from '~/domain/utils/either'
import { UserRegister } from './user-register'
import { getApiRoute } from '~/application/api/api-routes'
import type { HandledHttpClient } from '~/infra/http/http-client'
import { makeHttpClient } from '~/infra'
import { BadRequestException } from '~/domain/exceptions/bad-request.exception'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestFailureResult,
  TestSuccessResult
} from '~/main/config/tests/types/result-types'

const makeSut = () => {
  const httpClient: HandledHttpClient = makeHttpClient()
  const route = getApiRoute('register-user')
  const userRegister = new UserRegister(httpClient, route)

  return {
    userRegister,
    url: route().url,
    httpClient
  }
}

describe('UserRegister', () => {
  serverStarter()

  it('Should return error() if RequestResponse fails', async () => {
    const { userRegister } = makeSut()

    const params = {
      email: 'johndue.com',
      username: 'johndue',
      password: 'qwe123@QWE'
    }

    const response = (await userRegister.execute(
      params
    )) as TestFailureResult<UserRegister.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(
      new BadRequestException({
        error: {
          message: 'Invalid input data',
          success: false
        },
        type: 'BadRequestException',
        title: 'Bad request',
        message: 'Bad request'
      })
    )
  })

  it('Should return success() with correct values', async () => {
    const { userRegister } = makeSut()

    const params = {
      email: '<EMAIL>',
      username: 'johndue',
      password: 'qwe123QWE'
    }

    const response = (await userRegister.execute(
      params
    )) as TestSuccessResult<UserRegister.Response>

    expect(response.isSuccess()).toBe(true)
    expect(response).toEqual(
      success({
        message: 'Successfully registered user',
        success: true
      })
    )
  })
})
