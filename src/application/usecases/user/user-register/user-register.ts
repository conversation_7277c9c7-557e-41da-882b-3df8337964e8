import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class UserRegister implements UseCase<UserRegister.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute(
    params: UserRegister.Params
  ): UseCase.Response<UserRegister.Response> {
    const route = this.route()

    const responseOrError =
      await this.httpClient.request<UserRegister.Response>({
        authorized: true,
        method: route.method,
        url: route.url,
        body: params
      })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace UserRegister {
  export type Params = {
    username: string
    email: string
    password: string
  }

  export type Response = void
}
