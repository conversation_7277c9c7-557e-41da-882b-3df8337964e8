import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class GetSystemConfig implements UseCase<GetSystemConfig.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute(): UseCase.Response<GetSystemConfig.Response> {
    const route = this.route()
    const responseOrError =
      await this.httpClient.request<GetSystemConfig.Response>({
        authorized: false,
        method: route.method,
        url: route.url
      })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace GetSystemConfig {
  export type Response = Record<string, unknown>
  export type LocalStorageSystemInfo =
    | (GetSystemConfig.Response & {
        updatedDate: number
      })
    | undefined
}
