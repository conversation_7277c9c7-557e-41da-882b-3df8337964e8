import { failure, Success, success } from '~/domain/utils/either'

import { GetSystemConfig } from './get-system-config'
import { makeHttpClient } from '~/infra'
import type { HandledHttpClient } from '~/infra/http/http-client'
import { getApiRoute } from '~/application/api/api-routes'

import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'

const makeSut = () => {
  const httpClient: HandledHttpClient = makeHttpClient()
  const route = getApiRoute('system-config')
  const systemConfig = new GetSystemConfig(httpClient, route)

  const exceptionError = new UnexpectedException({
    error: '',
    message: 'Unexpected error',
    title: 'Unexpected error',
    type: 'UnexpectedException'
  })

  return {
    systemConfig,
    url: route().url,
    httpClient,
    exceptionError
  }
}

describe('GetSystemConfig', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const { systemConfig } = makeSut()

    const response =
      (await systemConfig.execute()) as TestSuccessResult<GetSystemConfig.Response>

    expect(response.isSuccess()).toBe(true)
    expect(response).toEqual(
      success({
        version: '1.0.0',
        updatedDate: 1627890123456,
        environment: 'development'
      })
    )
  })

  it('Should return failure() on error', async () => {
    const { systemConfig, httpClient, exceptionError } = makeSut()

    vi.spyOn(httpClient, 'request').mockResolvedValueOnce(
      failure(exceptionError)
    )

    const response =
      (await systemConfig.execute()) as TestFailureResult<GetSystemConfig.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(exceptionError)
  })
})
