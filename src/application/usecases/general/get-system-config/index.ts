import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeHttpClient } from '~/infra'

import { GetSystemConfig } from './get-system-config'

export const makeGetSystemConfigService =
  (): UseCase<GetSystemConfig.Response> =>
    new GetSystemConfig(makeHttpClient(), getApiRoute('system-config'))

export type { GetSystemConfig }
