import { type ApiRouteConfig } from '~/application/api/api-routes'
import type { UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class DeleteRoleUser implements UseCase<DeleteRoleUser.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig<{ roleId: string; userId: string }>
  ) {}

  async execute({
    roleId,
    userId
  }: DeleteRoleUser.Params): UseCase.Response<DeleteRoleUser.Response> {
    const route = this.route({ roleId, userId })
    const responseOrError =
      await this.httpClient.request<DeleteRoleUser.Response>({
        authorized: false,
        method: route.method,
        url: route.url
      })

    if (responseOrError.isFailure()) {
      return Promise.reject(failure(responseOrError.error))
    }

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace DeleteRoleUser {
  export type Params = {
    roleId: string
    userId: string
  }

  export type Response = void
}
