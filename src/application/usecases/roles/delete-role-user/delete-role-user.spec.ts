import { type HandledHttpClient } from '~/infra/http/http-client'
import { DeleteRoleUser } from './delete-role-user'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'

import { failure } from '~/domain/utils/either'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'

describe('DeleteRoleUser', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('delete-role-user')
    const deleteRoleUser = new DeleteRoleUser(httpClient, route)

    const result = (await deleteRoleUser.execute({
      roleId: '8e0a1e73-8454-475c-b8ce-3d063d944cf6',
      userId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4117777'
    })) as TestSuccessResult<DeleteRoleUser.Response>

    expect(result.isSuccess()).toBe(true)

    expect(result.value).toEqual({
      message: 'Successfully delete user',
      success: true
    })
  })

  it('Should return failure() on error', async () => {
    const exceptionError = new UnexpectedException({
      error: '',
      message: 'Unexpected error',
      title: 'Unexpected error',
      type: 'UnexpectedException'
    })

    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('delete-role-user')
    const deleteRoleUser = new DeleteRoleUser(httpClient, route)

    vi.spyOn(httpClient, 'request').mockResolvedValueOnce(
      failure(exceptionError)
    )

    const params = {
      userId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4117777',
      roleId: '8e0a1e73-8454-475c-b8ce-3d063d944cf6'
    }

    try {
      await deleteRoleUser.execute({
        ...params
      })
    } catch (error) {
      const response = error as TestFailureResult<DeleteRoleUser.Response>

      expect(response.isFailure()).toBe(true)
      expect(response.error).toEqual(exceptionError)
    }
  })
})
