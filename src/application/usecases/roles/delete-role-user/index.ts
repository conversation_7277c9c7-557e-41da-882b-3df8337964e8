import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { DeleteRoleUser } from './delete-role-user'

export const makeDeleteRoleUserService = (): UseCase<
  DeleteRoleUser.Response,
  DeleteRoleUser.Params
> => new DeleteRoleUser(makeTenantHttpClient(), getApiRoute('delete-role-user'))

export type { DeleteRoleUser }
