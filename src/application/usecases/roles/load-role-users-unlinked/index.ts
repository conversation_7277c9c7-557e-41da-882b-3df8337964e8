import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { LoadRoleUsersUnLinked } from './load-role-users-unlinked'

export const makeLoadRoleUsersUnLinkedService = (): UseCase<
  LoadRoleUsersUnLinked.Response,
  LoadRoleUsersUnLinked.Params
> =>
  new LoadRoleUsersUnLinked(
    makeTenantHttpClient(),
    getApiRoute('load-role-users-unlinked')
  )

export type { LoadRoleUsersUnLinked }
