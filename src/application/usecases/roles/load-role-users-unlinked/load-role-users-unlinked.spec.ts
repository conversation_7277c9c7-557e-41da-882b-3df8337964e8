import { type HandledHttpClient } from '~/infra/http/http-client'
import { LoadRoleUsersUnLinked } from './load-role-users-unlinked'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'
import { failure } from '~/domain/utils/either'

describe('LoadRoleUsersUnLinked', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-role-users-unlinked')
    const loadRoleUsersUnLinked = new LoadRoleUsersUnLinked(httpClient, route)

    const result = (await loadRoleUsersUnLinked.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      keywords: 'Due'
    })) as TestSuccessResult<LoadRoleUsersUnLinked.Response>

    expect(result.isSuccess()).toBe(true)
    expect(result.value).toEqual([
      {
        id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4115555',
        name: 'John Due',
        email: '<EMAIL>',
        status: 'USER_ACTIVE'
      },
      {
        id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4115554',
        name: 'Jane Due',
        email: '<EMAIL>',
        status: 'USER_ACTIVE'
      }
    ])
  })

  it('Should return failure() on error', async () => {
    const exceptionError = new UnexpectedException({
      error: '',
      message: 'Unexpected error',
      title: 'Unexpected error',
      type: 'UnexpectedException'
    })

    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-role-users-unlinked')
    const loadRoleUsersUnLinked = new LoadRoleUsersUnLinked(httpClient, route)

    vi.spyOn(httpClient, 'request').mockResolvedValueOnce(
      failure(exceptionError)
    )

    const response = (await loadRoleUsersUnLinked.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      keywords: 'Due'
    })) as TestFailureResult<LoadRoleUsersUnLinked.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(exceptionError)
  })

  it('Should return empty array when not found keyworks', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-role-users-unlinked')
    const loadRoleUsersUnLinked = new LoadRoleUsersUnLinked(httpClient, route)

    const response = (await loadRoleUsersUnLinked.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      keywords: 'not_found'
    })) as TestSuccessResult<LoadRoleUsersUnLinked.Response>

    expect(response.isSuccess()).toBe(true)
    expect(response.value).toEqual([])
  })
})
