import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import type { LoadRolesModel } from '~/domain/models/load-roles-model'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'
import type { PaginationParams } from '~/main/types/pagination-params'
import type { Paginated } from '~/main/types/paginated-type'

export class LoadRoles implements UseCase<LoadRoles.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute(
    params: LoadRoles.Params
  ): UseCase.Response<LoadRoles.Response> {
    const query = this.mountQuery(params)
    const route = this.route()

    const responseOrError = await this.httpClient.request<LoadRoles.Response>({
      authorized: false,
      method: route.method,
      url: `${route.url}?${query}`
    })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }

  private mountQuery(params: LoadRoles.Params): string {
    const query = Object.keys(params)
      .filter((key) => !!params[key as keyof LoadRoles.Params])
      .map(
        (key) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(params[key as keyof LoadRoles.Params] as string)}`
      )
      .join('&')

    return query
  }
}

export namespace LoadRoles {
  export type Params = PaginationParams
  export type Response = Paginated<LoadRolesModel[]>
}
