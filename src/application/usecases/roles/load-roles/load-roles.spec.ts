import { type HandledHttpClient } from '~/infra/http/http-client'
import { LoadRoles } from './load-roles'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'
import { failure } from '~/domain/utils/either'

describe('LoadRoles', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('role')
    const loadRoles = new LoadRoles(httpClient, route)

    const params = {
      pageSize: 10,
      page: 1
    }

    const result = (await loadRoles.execute({
      ...params
    })) as TestSuccessResult<LoadRoles.Response>

    expect(result.isSuccess()).toBe(true)
  })

  it('Should return failure() on error', async () => {
    const exceptionError = new UnexpectedException({
      error: '',
      message: 'Unexpected error',
      title: 'Unexpected error',
      type: 'UnexpectedException'
    })

    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('role')
    const loadRoles = new LoadRoles(httpClient, route)

    vi.spyOn(httpClient, 'request').mockResolvedValueOnce(
      failure(exceptionError)
    )

    const response = (await loadRoles.execute({
      page: 1000,
      pageSize: 1000
    })) as TestFailureResult<LoadRoles.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(exceptionError)
  })
})
