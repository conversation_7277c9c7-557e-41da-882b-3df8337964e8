import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class LoadRole implements UseCase<LoadRole.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig<{
      roleId: string
    }>
  ) {}

  async execute({
    roleId
  }: LoadRole.Params): UseCase.Response<LoadRole.Response> {
    const route = this.route({ roleId })

    const responseOrError = await this.httpClient.request<LoadRole.Response>({
      authorized: false,
      method: route.method,
      url: route.url
    })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace LoadRole {
  export type Params = { roleId: string }
  export type Response = {
    name: string
    id: string
    isAdminDefault: boolean
    isUserDefault: boolean
  }
}
