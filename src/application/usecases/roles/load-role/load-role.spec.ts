import { type HandledHttpClient } from '~/infra/http/http-client'
import { LoadRole } from './load-role'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'
import { failure } from '~/domain/utils/either'

describe('LoadRole', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-role')
    const loadRole = new LoadRole(httpClient, route)

    const result = (await loadRole.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4117777'
    })) as TestSuccessResult<LoadRole.Response>

    expect(result.isSuccess()).toBe(true)
    expect(result.value).toEqual({
      name: 'John Due',
      id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      isAdminDefault: true,
      isUserDefault: true
    })
  })

  it('Should return failure() on error', async () => {
    const exceptionError = new UnexpectedException({
      error: '',
      message: 'Unexpected error',
      title: 'Unexpected error',
      type: 'UnexpectedException'
    })

    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-role')
    const loadRole = new LoadRole(httpClient, route)

    vi.spyOn(httpClient, 'request').mockResolvedValueOnce(
      failure(exceptionError)
    )

    const response = (await loadRole.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4117777'
    })) as TestFailureResult<LoadRole.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(exceptionError)
  })
})
