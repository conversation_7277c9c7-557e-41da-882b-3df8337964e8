import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class UpdateRole implements UseCase<UpdateRole.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig<{
      roleId: string
    }>
  ) {}

  async execute({
    roleId,
    ...params
  }: UpdateRole.Params): UseCase.Response<UpdateRole.Response> {
    const route = this.route({ roleId })

    const responseOrError = await this.httpClient.request<UpdateRole.Response>({
      authorized: false,
      method: route.method,
      url: route.url,
      body: params
    })

    if (responseOrError.isFailure()) {
      return Promise.reject(failure(responseOrError.error))
    }

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace UpdateRole {
  export type Params = { roleId: string; name: string }
  export type Response = {
    name: string
    id: string
    isAdminDefault: boolean
    isUserDefault: boolean
  }
}
