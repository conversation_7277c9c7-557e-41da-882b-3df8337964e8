import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { UpdateRole } from './update-role'

export const makeUpdateRoleService = (): UseCase<
  UpdateRole.Response,
  UpdateRole.Params
> => new UpdateRole(makeTenantHttpClient(), getApiRoute('update-role'))

export type { UpdateRole }
