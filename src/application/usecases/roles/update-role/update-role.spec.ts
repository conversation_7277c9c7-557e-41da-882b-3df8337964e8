import { type HandledHttpClient } from '~/infra/http/http-client'
import { UpdateRole } from './update-role'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'

describe('UpdateRole', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('update-role')
    const updateRole = new UpdateRole(httpClient, route)

    const result = (await updateRole.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      name: '<PERSON>'
    })) as TestSuccessResult<UpdateRole.Response>

    expect(result.isSuccess()).toBe(true)

    expect(result.value).toEqual({
      message: 'Successfully update role',
      success: true
    })
  })

  it('Should return failure() on error', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('update-role')
    const updateRole = new UpdateRole(httpClient, route)

    try {
      await updateRole.execute({
        roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
        name: 'invalid_name'
      })
    } catch (error) {
      const response = error as TestFailureResult<UpdateRole.Response>

      expect(response.isFailure()).toBe(true)
    }
  })
})
