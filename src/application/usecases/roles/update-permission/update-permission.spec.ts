import { type HandledHttpClient } from '~/infra/http/http-client'
import { UpdatePermission } from './update-permission'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'

describe('UpdatePermission', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('role-update')
    const updateRolePermission = new UpdatePermission(httpClient, route)

    const result = (await updateRolePermission.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      active: true,
      id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4114444',
      type: 'group'
    })) as TestSuccessResult<UpdatePermission.Response>

    expect(result.isSuccess()).toBe(true)

    expect(result.value).toEqual({
      message: 'Successfully update role',
      success: true
    })
  })

  it('Should return failure() on error', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('role-update')
    const updateRolePermission = new UpdatePermission(httpClient, route)

    const response = (await updateRolePermission.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      active: true,
      id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4114445',
      type: 'permission'
    })) as TestFailureResult<UpdatePermission.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error.error).toEqual({
      message: 'Invalid data',
      success: false
    })
  })
})
