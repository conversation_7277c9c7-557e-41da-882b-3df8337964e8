import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'

import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'
import type { AvailableLanguages } from '~/presentation/types'

export class UpdatePermission implements UseCase<UpdatePermission.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig<{
      roleId: string
    }>
  ) {}

  async execute({
    roleId,
    ...params
  }: UpdatePermission.Params): UseCase.Response<UpdatePermission.Response> {
    const route = this.route({ roleId })

    const responseOrError =
      await this.httpClient.request<UpdatePermission.Response>({
        authorized: false,
        method: route.method,
        url: route.url,
        body: params
      })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace UpdatePermission {
  export type Params = {
    id: string
    type: 'permission' | 'group'
    active: boolean
    roleId: string
  }
  export type Response = void
}
