import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { UpdatePermission } from './update-permission'

export const makeUpdateRolesPermissionsService = (): UseCase<
  UpdatePermission.Response,
  UpdatePermission.Params
> => new UpdatePermission(makeTenantHttpClient(), getApiRoute('role-update'))

export type { UpdatePermission }
