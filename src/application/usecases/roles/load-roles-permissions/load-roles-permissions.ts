import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'
import type { PermissionsGroup } from '~/domain/models/roles/load-roles-permissions-model'
import type { AvailableLanguages } from '~/presentation/types'

export class LoadRolesPermissions
  implements UseCase<LoadRolesPermissions.Response>
{
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig<{
      roleId: string
    }>
  ) {}

  async execute({
    roleId,
    language,
    ...params
  }: LoadRolesPermissions.Params): UseCase.Response<LoadRolesPermissions.Response> {
    const query = this.mountQuery(params)
    const route = this.route({ roleId: roleId })

    const responseOrError =
      await this.httpClient.request<LoadRolesPermissions.Response>({
        authorized: false,
        method: route.method,
        url: `${route.url}${`?${query}`}`,
        headers: {
          language
        }
      })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }

  private mountQuery(
    params: Pick<LoadRolesPermissions.Params, 'keywords'>
  ): string {
    const query = Object.keys(params)
      .filter(
        (key) =>
          !!params[key as keyof Pick<LoadRolesPermissions.Params, 'keywords'>]
      )
      .map(
        (key) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(params[key as keyof Pick<LoadRolesPermissions.Params, 'keywords'>] as string)}`
      )
      .join('&')

    return query
  }
}

export namespace LoadRolesPermissions {
  export type Params = {
    roleId: string
    language: AvailableLanguages
    keywords?: string
  }
  export type Response = PermissionsGroup
}
