import { type HandledHttpClient } from '~/infra/http/http-client'
import { LoadRolesPermissions } from './load-roles-permissions'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'
import { failure } from '~/domain/utils/either'

describe('LoadRoleUsers', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-roles')
    const loadRolesPermissions = new LoadRolesPermissions(httpClient, route)

    const result = (await loadRolesPermissions.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      language: 'en-US'
    })) as TestSuccessResult<LoadRolesPermissions.Response>

    expect(result.isSuccess()).toBe(true)
    expect(result.value).toEqual([
      {
        id: '29455e54-cdf5-4720-8494-b4046806b053',
        name: 'Access',
        type: 'group',
        children: [
          {
            id: '0ce28ad7-2a22-43cd-8b16-d75b5764660d',
            name: 'Permission profile',
            type: 'group',
            children: [
              {
                id: '1fdab142-50b4-4b8f-938e-97c02afd24ec',
                name: 'Create permission profile',
                action: 'Create',
                type: 'permission',
                isActive: true
              }
            ]
          }
        ]
      }
    ])
  })

  it('Should return failure() on error', async () => {
    const exceptionError = new UnexpectedException({
      error: '',
      message: 'Unexpected error',
      title: 'Unexpected error',
      type: 'UnexpectedException'
    })

    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-roles')
    const loadRolesPermissions = new LoadRolesPermissions(httpClient, route)

    vi.spyOn(httpClient, 'request').mockResolvedValueOnce(
      failure(exceptionError)
    )

    const response = (await loadRolesPermissions.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      language: 'en-US'
    })) as TestFailureResult<LoadRolesPermissions.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(exceptionError)
  })

  it('Should return empty array when not found keyworks', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-roles')
    const loadRolesPermissions = new LoadRolesPermissions(httpClient, route)

    const response = (await loadRolesPermissions.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      language: 'en-US',
      keywords: 'not_found_permission'
    })) as TestSuccessResult<LoadRolesPermissions.Response>

    expect(response.isSuccess()).toBe(true)
    expect(response.value).toEqual([])
  })
})
