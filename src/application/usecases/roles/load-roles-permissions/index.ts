import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { LoadRolesPermissions } from './load-roles-permissions'

export const makeLoadRolesPermissionsService = (): UseCase<
  LoadRolesPermissions.Response,
  LoadRolesPermissions.Params
> => new LoadRolesPermissions(makeTenantHttpClient(), getApiRoute('load-roles'))

export type { LoadRolesPermissions }
