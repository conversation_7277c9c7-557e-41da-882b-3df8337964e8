import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { LoadRoleUsers } from './load-role-users'

export const makeLoadRoleUsersService = (): UseCase<
  LoadRoleUsers.Response,
  LoadRoleUsers.Params
> => new LoadRoleUsers(makeTenantHttpClient(), getApiRoute('load-role-users'))

export type { LoadRoleUsers }
