import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import type { LoadRoleUsersModel } from '~/domain/models/roles/load-role-users-model'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'
import type { PaginationParams } from '~/main/types/pagination-params'
import type { Paginated } from '~/main/types/paginated-type'

export class LoadRoleUsers implements UseCase<LoadRoleUsers.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig<{
      roleId: string
    }>
  ) {}

  async execute({
    roleId,
    ...params
  }: LoadRoleUsers.Params): UseCase.Response<LoadRoleUsers.Response> {
    const query = this.mountQuery(params)
    const route = this.route({ roleId })

    const responseOrError =
      await this.httpClient.request<LoadRoleUsers.Response>({
        authorized: false,
        method: route.method,
        url: `${route.url}?${query}`
      })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }

  private mountQuery(params: Omit<LoadRoleUsers.Params, 'roleId'>): string {
    const query = Object.keys(params)
      .filter(
        (key) => !!params[key as keyof Omit<LoadRoleUsers.Params, 'roleId'>]
      )
      .map(
        (key) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(params[key as keyof Omit<LoadRoleUsers.Params, 'roleId'>] as string)}`
      )
      .join('&')

    return query
  }
}

export namespace LoadRoleUsers {
  export type Params = PaginationParams & {
    roleId: string
  }
  export type Response = Paginated<LoadRoleUsersModel[]>
}
