import { type HandledHttpClient } from '~/infra/http/http-client'
import { LoadRoleUsers } from './load-role-users'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'
import { failure } from '~/domain/utils/either'

describe('LoadRoleUsers', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-role-users')
    const loadRoleUsers = new LoadRoleUsers(httpClient, route)

    const params = {
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      page: 1,
      pageSize: 10
    }

    const result = (await loadRoleUsers.execute({
      ...params
    })) as TestSuccessResult<LoadRoleUsers.Response>

    expect(result.isSuccess()).toBe(true)
    expect(result.value.rows).toEqual([
      {
        id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4115555',
        name: 'John Due',
        email: '<EMAIL>',
        status: 'USER_ACTIVE'
      },
      {
        id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4115554',
        name: 'Jane Due',
        email: '<EMAIL>',
        status: 'USER_ACTIVE'
      }
    ])
  })

  it('Should return failure() on error', async () => {
    const exceptionError = new UnexpectedException({
      error: '',
      message: 'Unexpected error',
      title: 'Unexpected error',
      type: 'UnexpectedException'
    })

    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('load-role-users')
    const loadRoleUsers = new LoadRoleUsers(httpClient, route)

    vi.spyOn(httpClient, 'request').mockResolvedValueOnce(
      failure(exceptionError)
    )

    const response = (await loadRoleUsers.execute({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      page: 1,
      pageSize: 10
    })) as TestFailureResult<LoadRoleUsers.Response>

    expect(response.isFailure()).toBe(true)
    expect(response.error).toEqual(exceptionError)
  })
})
