import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { AddRoleUser } from './add-role-user'

export const makeAddRoleUserService = (): UseCase<
  AddRoleUser.Response,
  AddRoleUser.Params
> => new AddRoleUser(makeTenantHttpClient(), getApiRoute('add-role-user'))

export type { AddRoleUser }
