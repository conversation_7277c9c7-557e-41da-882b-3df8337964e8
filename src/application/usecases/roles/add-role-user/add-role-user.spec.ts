import { type HandledHttpClient } from '~/infra/http/http-client'
import { AddRoleUser } from './add-role-user'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'
import { NotFoundException } from '~/domain/exceptions/not-found.exception'

describe('AddRoleUser', () => {
  serverStarter()

  it('should return success when valid params are provided', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('add-role-user')
    const addRoleUser = new AddRoleUser(httpClient, route)

    const params = {
      id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4117777',
      roleId: '999e8888-7c1e-4a2d-afd3-c2ffb4117777'
    }
    const result = (await addRoleUser.execute(
      params
    )) as TestSuccessResult<AddRoleUser.Response>

    expect(result.isSuccess()).toBe(true)

    expect(result.value).toEqual({
      message: 'Successfully associated user',
      success: true
    })
  })

  it('should return failure when invalid params are provided', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('add-role-user')
    const addRoleUser = new AddRoleUser(httpClient, route)

    const params = {
      id: '999e8888-7c1e-4a2d-afd3-c2ffb4117777',
      roleId: '999e8888-7c1e-4a2d-afd3-c2ffb4117777'
    }

    try {
      await addRoleUser.execute(params)
    } catch (error) {
      const response = error as TestFailureResult<AddRoleUser.Response>

      expect(response.isFailure()).toBe(true)
      expect(response.error).toEqual(
        new NotFoundException({
          error: {
            message: 'Not found',
            success: false
          },
          type: 'NotFoundException',
          title: 'Not found',
          message: 'Not found'
        })
      )
    }
  })
})
