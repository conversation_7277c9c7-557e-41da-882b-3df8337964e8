import { type ApiRouteConfig } from '~/application/api/api-routes'
import type { UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class AddRoleUser implements UseCase<AddRoleUser.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig<{ roleId: string }>
  ) {}

  async execute({
    roleId,
    ...params
  }: AddRoleUser.Params): UseCase.Response<AddRoleUser.Response> {
    const route = this.route({ roleId })
    const responseOrError = await this.httpClient.request<AddRoleUser.Response>(
      {
        authorized: false,
        method: route.method,
        url: route.url,
        body: params
      }
    )

    if (responseOrError.isFailure()) {
      return Promise.reject(failure(responseOrError.error))
    }

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace AddRoleUser {
  export type Params = {
    id: string
    roleId: string
  }

  export type Response = void
}
