import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { LoadRoleUsersLinked } from './load-role-users-linked'

export const makeLoadRoleUsersLinkedService = (): UseCase<
  LoadRoleUsersLinked.Response,
  LoadRoleUsersLinked.Params
> =>
  new LoadRoleUsersLinked(
    makeTenantHttpClient(),
    getApiRoute('load-role-users-linked')
  )

export type { LoadRoleUsersLinked }
