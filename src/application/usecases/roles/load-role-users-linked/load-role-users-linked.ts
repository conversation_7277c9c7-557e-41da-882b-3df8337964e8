import { type ApiRouteConfig } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import type { LoadRoleUsersModel } from '~/domain/models/roles/load-role-users-model'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'
import type { PaginationParams } from '~/main/types/pagination-params'

export class LoadRoleUsersLinked
  implements UseCase<LoadRoleUsersLinked.Response>
{
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig<{
      roleId: string
    }>
  ) {}

  async execute({
    roleId,
    ...params
  }: LoadRoleUsersLinked.Params): UseCase.Response<LoadRoleUsersLinked.Response> {
    const query = this.mountQuery(params)
    const route = this.route({ roleId })

    const responseOrError =
      await this.httpClient.request<LoadRoleUsersLinked.Response>({
        authorized: false,
        method: route.method,
        url: `${route.url}?${query}`
      })

    if (responseOrError.isFailure()) return failure(responseOrError.error)

    const response = responseOrError.value.response

    return success(response)
  }

  private mountQuery(params: Pick<PaginationParams, 'keywords'>): string {
    const query = Object.keys(params)
      .filter(
        (key) => !!params[key as keyof Pick<PaginationParams, 'keywords'>]
      )
      .map(
        (key) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(params[key as keyof Pick<PaginationParams, 'keywords'>] as string)}`
      )
      .join('&')

    return query
  }
}

export namespace LoadRoleUsersLinked {
  export type Params = {
    roleId: string
    keywords: string
  }
  export type Response = LoadRoleUsersModel[]
}
