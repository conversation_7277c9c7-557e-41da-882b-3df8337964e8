import { getApiRoute } from '~/application/api/api-routes'
import { type UseCase } from '~/application/contracts/usecase'
import { makeTenantHttpClient } from '~/infra/http'

import { CreateRole } from './create-role'

export const makeCreateRoleService = (): UseCase<
  CreateRole.Response,
  CreateRole.Params
> => new CreateRole(makeTenantHttpClient(), getApiRoute('create-role'))

export type { CreateRole }
