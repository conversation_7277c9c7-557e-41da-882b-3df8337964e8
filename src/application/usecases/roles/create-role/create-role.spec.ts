import { type HandledHttpClient } from '~/infra/http/http-client'
import { CreateRole } from './create-role'
import { getApiRoute } from '~/application/api/api-routes'
import { makeTenantHttpClient } from '~/infra'
import { serverStarter } from '~/main/config/tests/utils/server-starter'
import type {
  TestSuccessResult,
  TestFailureResult
} from '~/main/config/tests/types/result-types'

describe('CreateRole', () => {
  serverStarter()

  it('Should return success() with correct values', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('create-role')
    const createRole = new CreateRole(httpClient, route)

    const result = (await createRole.execute({
      name: 'Fake Role'
    })) as TestSuccessResult<CreateRole.Response>

    expect(result.isSuccess()).toBe(true)

    expect(result.value).toEqual({
      message: 'Successfully create role',
      success: true
    })
  })

  it('Should return failure() on error', async () => {
    const httpClient: HandledHttpClient = makeTenantHttpClient()
    const route = getApiRoute('create-role')
    const createRole = new CreateRole(httpClient, route)

    try {
      await createRole.execute({
        name: 'invalid_role'
      })
    } catch (error) {
      const response = error as TestFailureResult<CreateRole.Response>

      expect(response.isFailure()).toBe(true)
      expect(response.error.error).toEqual({
        message: 'Invalid data',
        success: false
      })
    }
  })
})
