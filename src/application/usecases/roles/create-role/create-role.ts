import { type ApiRouteConfig } from '~/application/api/api-routes'
import type { UseCase } from '~/application/contracts/usecase'
import { failure, success } from '~/domain/utils/either'
import { type HandledHttpClient } from '~/infra/http/http-client'

export class CreateRole implements UseCase<CreateRole.Response> {
  constructor(
    private readonly httpClient: HandledHttpClient,
    private readonly route: ApiRouteConfig
  ) {}

  async execute({
    ...params
  }: CreateRole.Params): UseCase.Response<CreateRole.Response> {
    const route = this.route()
    const responseOrError = await this.httpClient.request<CreateRole.Response>({
      authorized: false,
      method: route.method,
      url: route.url,
      body: params
    })

    if (responseOrError.isFailure()) {
      return Promise.reject(failure(responseOrError.error))
    }

    const response = responseOrError.value.response

    return success(response)
  }
}

export namespace CreateRole {
  export type Params = {
    name: string
  }

  export type Response = void
}
