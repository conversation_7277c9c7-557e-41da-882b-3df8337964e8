import { AccessDeniedException } from '~/domain/exceptions/access-denied.exception'
import { BadRequestException } from '~/domain/exceptions/bad-request.exception'
import { type DomainException } from '~/domain/exceptions/domain-exception'
import { isConflictException } from '~/domain/exceptions/is-conflict.exception'
import { NotFoundException } from '~/domain/exceptions/not-found.exception'
import { UnexpectedException } from '~/domain/exceptions/unexpected-error.exception'
import {
  type CombinedPredicated,
  combinedPredicates
} from '~/domain/utils/combined-predicates'
import { type Either, failure, success } from '~/domain/utils/either'
import { type HttpResponse, HttpStatusCode } from '~/infra/http/http-client'

export class RequestResponse<T> {
  private constructor(private readonly _response: T) {
    Object.freeze(this)
  }

  public static handle<R>(
    httpResponse: HttpResponse<R>
  ): Either<DomainException, RequestResponse<R>> {
    try {
      const { statusCode } = httpResponse

      if (this.isSuccess(statusCode)) {
        const response = httpResponse.body as R

        return success(new RequestResponse<R>(response))
      }
      const predicates: CombinedPredicated<DomainException> = [
        [
          this.isUnauthorized,
          new AccessDeniedException({
            error: httpResponse.error,
            message: 'Unauthorized',
            title: 'Unauthorized',
            type: 'UnauthorizedException'
          })
        ],
        [
          this.isForbidden,
          new AccessDeniedException({
            error: httpResponse.error,
            message: 'Forbidden',
            title: 'Forbidden',
            type: 'ForbiddenException'
          })
        ],
        [
          this.isNotFound,
          new NotFoundException({
            error: httpResponse.error,
            message: 'Not found',
            title: 'Not found',
            type: 'NotFoundException'
          })
        ],
        [
          this.isBadRequest,
          new BadRequestException({
            error: httpResponse.error,
            message: 'Bad request',
            title: 'Bad request',
            type: 'BadRequestException'
          })
        ],
        [
          this.isConflict,
          new isConflictException({
            error: httpResponse.error,
            message: httpResponse.error?.errors,
            title: 'Conflict',
            type: 'isConflictException'
          })
        ]
      ]

      const errors = combinedPredicates({
        value: statusCode,
        predicatePairs: predicates
      })

      if (errors.isFailure()) {
        return failure(errors.error)
      }

      return failure(
        new UnexpectedException({
          error: httpResponse.body,
          message: 'Unexpected error',
          title: 'Unexpected error',
          type: 'UnexpectedException'
        })
      )
    } catch (err) {
      return failure(
        new UnexpectedException({
          error: err,
          message: 'Unexpected error',
          title: 'Unexpected error',
          type: 'UnexpectedException'
        })
      )
    }
  }

  private static isSuccess(statusCode: HttpStatusCode) {
    return statusCode >= 200 && statusCode <= 299
  }

  private static isForbidden(statusCode: HttpStatusCode): boolean {
    return statusCode === HttpStatusCode.FORBIDDEN
  }

  private static isUnauthorized(statusCode: HttpStatusCode): boolean {
    return statusCode === HttpStatusCode.UNAUTHORIZED
  }

  private static isNotFound(statusCode: HttpStatusCode): boolean {
    return statusCode === HttpStatusCode.NOT_FOUND
  }

  private static isBadRequest(statusCode: HttpStatusCode): boolean {
    return statusCode === HttpStatusCode.BAD_REQUEST
  }

  private static isConflict(statusCode: HttpStatusCode): boolean {
    return statusCode === HttpStatusCode.IS_CONFLICT
  }

  private static isPromise<T>(value: any): value is Promise<T> {
    return value instanceof Promise
  }

  get response(): T {
    return this._response
  }
}
