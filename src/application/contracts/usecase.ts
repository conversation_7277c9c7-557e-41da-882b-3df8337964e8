import { type RequestResponse } from '~/application/utils/http-response'
import { type DomainException } from '~/domain/exceptions/domain-exception'
import { type Either } from '~/domain/utils/either'

export interface UseCase<T = any, R = never> {
  execute: UseCase.Execute<T, R>
}

export namespace UseCase {
  export type Execute<T, R = never> = (
    ...args: R[]
  ) => Promise<Either<DomainException, RequestResponse<T>['response']>>

  export type Response<T> = Promise<
    Either<DomainException, RequestResponse<T>['response']>
  >
}
