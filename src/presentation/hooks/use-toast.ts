import { useState, useEffect, useCallback, type ReactNode } from 'react'
import { useTranslation } from './use-translation'

type Props = {
  initialIsOpen?: boolean
  autoCloseDelay?: number
}

export const useToast = ({
  initialIsOpen = false,
  autoCloseDelay = 5000
}: Props = {}) => {
  const [isToastOpen, setIsToastOpen] = useState(initialIsOpen)
  const [toastDescription, setToastDescription] = useState<string | ReactNode>(
    ''
  )
  const [toastVariant, setToastVariant] = useState<'success' | 'error'>('error')
  const { translate } = useTranslation()

  const showToast = useCallback(
    (
      description?: string | ReactNode,
      variant: 'success' | 'error' = 'error'
    ) => {
      setToastDescription(description ?? translate('create_error_alert_msg'))
      setToastVariant(variant)
      setIsToastOpen(true)
    },
    []
  )

  const hideToast = useCallback(() => {
    setIsToastOpen(false)
  }, [])

  useEffect(() => {
    if (isToastOpen) {
      const timer = setTimeout(() => {
        hideToast()
      }, autoCloseDelay)
      return () => clearTimeout(timer)
    }
  }, [isToastOpen, hideToast, autoCloseDelay])

  return {
    isToastOpen,
    showToast,
    hideToast,
    toastDescription,
    toastVariant
  }
}
