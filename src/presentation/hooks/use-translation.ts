import { useTranslation as i18NextUseTranslation } from 'react-i18next'

import { type TOptions } from 'i18next'

import {
  type AvailableLanguages,
  type Translation,
  type TFunction
} from '../types'

export const useTranslation = (namespace?: string | string[]): Translation => {
  const {
    t,
    i18n: { language, changeLanguage }
  } = i18NextUseTranslation(namespace)

  const translate: TFunction<
    string | string[],
    any,
    string | TOptions<string[]> | undefined
  > = (...args): string => t(args)

  const updateLanguage = (newLanguage: AvailableLanguages): void => {
    changeLanguage(newLanguage)
  }

  return {
    translate,
    language,
    updateLanguage
  }
}
