import type { QueryClient } from '@tanstack/react-query'
import { type ActionFunctionArgs } from 'react-router'
import { validateSecurityTokenQuery } from '~/presentation/querys/validate-security-token-query'
import { confirmAccountQuery } from './query/confirm-account-query'
import { makeTokenDecoder } from '~/main/factories/make-token-decoder'
import type { JwtDecodedEntity } from '~/domain/entities/jwt-decoded-entity'

export const confirmEmailLoader =
  (queryClient: QueryClient) =>
  async ({ request }: ActionFunctionArgs) => {
    const url = new URL(request.url)
    const securityToken = url.searchParams.get('securityToken') as string

    const { decode } = makeTokenDecoder()

    const { email }: JwtDecodedEntity = decode(securityToken)

    await queryClient.ensureQueryData(
      validateSecurityTokenQuery({ securityToken })
    )

    await queryClient.ensureQueryData(confirmAccountQuery({ securityToken }))

    return {
      email,
      securityToken
    }
  }
