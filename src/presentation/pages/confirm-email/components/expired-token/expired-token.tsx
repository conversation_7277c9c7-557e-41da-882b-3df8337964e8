import { useState } from 'react'
import { But<PERSON> } from 'softo-design-system'
import { useSearchParams } from 'react-router'
import { makeResendConfirmAccountService } from '~/application/usecases/auth/resend-confirm-account'
import { Spinner } from '~/presentation/components/spinner/spinner'
import { When } from '~/presentation/components/when/when'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { makeTokenDecoder } from '~/main/factories/make-token-decoder'
import { useMutation } from '@tanstack/react-query'
import type { JwtDecodedEntity } from '~/domain/entities/jwt-decoded-entity'
import { SystemVersion } from '~/presentation/components/system-version/system-version'
import type { isConflictException } from '~/domain/exceptions/is-conflict.exception'

export const ExpiredToken = () => {
  const { translate } = useTranslation('confirm-email')
  const resendConfirmAccountService = makeResendConfirmAccountService()
  const [showMessageSuccess, setShowMessageSuccess] = useState('')
  const [searchParams] = useSearchParams()

  const securityToken = searchParams.get('securityToken') as string

  const { decode } = makeTokenDecoder()

  const { email }: JwtDecodedEntity = decode(securityToken)

  const {
    mutate: resendConfirmAccount,
    isSuccess,
    isPending
  } = useMutation({
    mutationFn: (email: string) => {
      return resendConfirmAccountService.execute({ email })
    },
    onError: (error: isConflictException) => {
      setShowMessageSuccess(error?.error?.error?.errors)
    }
  })

  const handleMessageError = () => {
    if (!showMessageSuccess?.length) return translate('expired_text')

    const errors: { [key: string]: string } = {
      'User email already confirmed': translate('user_email_already_confirmed'),
      'User was not found': translate('user_was_not_found')
    }

    return errors[showMessageSuccess]
  }

  const handleResendConfirmAccount = () => resendConfirmAccount(email)

  return (
    <section className='flex flex-col justify-between gap-32'>
      <div className='flex flex-col items-center justify-center'>
        <h1 className='text-center uppercase text-[28px] font-bold text-text-icon-high-emphasis'>
          {isSuccess ? translate('email_sent') : translate('label_ops')}
        </h1>
        <p className='text-sm mt-4 text-center'>
          {isSuccess ? translate('email_sent_text') : handleMessageError()}
        </p>
        <When condition={!isSuccess}>
          <Button
            className='uppercase mt-16'
            variant='borderless'
            onClick={handleResendConfirmAccount}
            disabled={isPending}
          >
            {isPending ? <Spinner /> : translate('resend_email_text')}
          </Button>
        </When>
      </div>

      <SystemVersion
        stylesTextVersions='font-semibold'
        stylesContainer='flex flex-col gap-2 w-full justify-center items-center mt-6 md:mt-15'
        stylesContainerVersions='flex flex-col gap-2'
      />
    </section>
  )
}
