import { queryOptions } from '@tanstack/react-query'
import { makeConfirmAccountService } from '~/application/usecases/auth/confirm-account'
import { createQueryFn } from '~/presentation/querys/helpers/create-query'

type Props = {
  securityToken: string
}

export const confirmAccountQuery = ({ securityToken }: Props) => {
  const confirmAccountService = makeConfirmAccountService()

  return queryOptions({
    queryKey: ['confirmAccount', securityToken],
    queryFn: createQueryFn(confirmAccountService, { securityToken })
  })
}
