import { But<PERSON> } from 'softo-design-system'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { Link } from 'react-router'

import { ROUTES } from '~/main/types'
import { SystemVersion } from '~/presentation/components/system-version/system-version'

const ConfirmEmail = () => {
  const { translate } = useTranslation('confirm-email')

  return (
    <section className='flex flex-col justify-between gap-32'>
      <div className='flex flex-col items-center justify-center'>
        <h1 className='text-center text-[28px] font-bold text-text-icon-high-emphasis'>
          {translate('confirm_email_title')}
        </h1>
        <p className='text-sm mt-4 text-center max-w-96'>
          {translate('confirm_email_subtitle')}
        </p>
        <Link to={ROUTES.LOGIN}>
          <Button className='uppercase mt-16' variant='borderless'>
            {translate('btn_go_to_login')}
          </Button>
        </Link>
      </div>

      <SystemVersion
        stylesTextVersions='font-semibold'
        stylesContainer='flex flex-col gap-2 w-full justify-center items-center mt-6 md:mt-15'
        stylesContainerVersions='flex flex-col gap-2'
      />
    </section>
  )
}

export default ConfirmEmail
