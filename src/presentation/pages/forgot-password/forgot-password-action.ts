import { type ActionFunctionArgs } from 'react-router'
import { makeForgotPasswordService } from '~/application/usecases'

export const forgotPasswordAction = async ({ request }: ActionFunctionArgs) => {
  const forgotPasswordService = makeForgotPasswordService()
  const { email } = Object.fromEntries(await request.formData())

  if (!email) return { error: 'email not found' }

  const responseOrError = await forgotPasswordService.execute({
    email: email as string
  })

  if (responseOrError.isFailure()) {
    return responseOrError.error
  }

  return { isSuccess: true }
}
