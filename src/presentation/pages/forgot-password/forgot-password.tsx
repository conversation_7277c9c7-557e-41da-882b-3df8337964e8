import { Button } from 'softo-design-system'
import { type ResetPasswordForm as Model } from '~/domain/models'
import { ROUTES } from '~/main/types'
import { Link, TextInput } from '~/presentation/components'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useNavigate, useFetcher } from 'react-router'
import { useTranslation } from 'react-i18next'
import { Spinner } from '~/presentation/components/spinner/spinner'
import { validationSchema } from './validations/forgot-password-form-validation'

const ForgotPassword = () => {
  const { t: translate } = useTranslation('forgot-password')

  const navigate = useNavigate()

  const fetcher = useFetcher()

  const {
    handleSubmit,
    register,
    watch,
    formState: { errors }
  } = useForm<Model>({
    resolver: zodResolver(validationSchema),
    mode: 'onChange'
  })

  const email = watch('email')
  const emailWasSent = fetcher?.data?.isSuccess

  const isLoading =
    fetcher?.state === 'loading' || fetcher?.state === 'submitting'

  const onSubmit = (data: Model) => {
    fetcher.submit(data, { method: 'post' })
  }

  const handleRedirectToLogin = () => navigate(ROUTES.LOGIN)

  return (
    <div className='flex flex-col max-w-96 mx-auto pb-10'>
      {!emailWasSent && (
        <fetcher.Form onSubmit={handleSubmit(onSubmit)}>
          <h1 className='mb-4 text-2xl md:text-[1.75rem] font-bold text-center uppercase'>
            {translate('reset_password_title')}
          </h1>
          <h3 className='mb-16 text-center'>
            {translate('reset_password_description')}
          </h3>
          <TextInput
            color='secondary'
            label={translate('label_field_email')}
            placeholder={translate('placeholder_field_email')}
            maxLength={300}
            error={errors?.email}
            {...register('email')}
          />
          <div className='w-full flex flex-col justify-center items-center'>
            <Button type='submit' className='w-full mb-4 uppercase'>
              {isLoading ? <Spinner /> : translate('btn_send')}
            </Button>
            <Link to={ROUTES.LOGIN}>
              <Button
                type='button'
                variant='borderless'
                onClick={handleRedirectToLogin}
                className='uppercase'
              >
                {translate('btn_back_to_login')}
              </Button>
            </Link>
          </div>
        </fetcher.Form>
      )}

      {emailWasSent && (
        <div className='flex flex-col justify-center items-center'>
          <h1 className='mb-4 text-2xl md:text-[1.75rem] font-bold text-center uppercase'>
            {translate('title_success')}
          </h1>
          <h3 className='mb-10 text-center text-sm'>
            {translate('description_success', { email })}
          </h3>
          <Link to={ROUTES.LOGIN}>
            <Button
              type='button'
              variant='borderless'
              onClick={handleRedirectToLogin}
              className='uppercase'
            >
              {translate('btn_back_to_login')}
            </Button>
          </Link>
        </div>
      )}
    </div>
  )
}

export default ForgotPassword
