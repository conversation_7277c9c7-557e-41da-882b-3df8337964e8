import { screen, render, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import ForgotPassword from './forgot-password'
import { vi } from 'vitest'
import {
  MemoryRouter,
  RouterProvider,
  createMemoryRouter
} from 'react-router'

const navigate = vi.fn()
const fetcher = {
  state: 'idle',
  data: { isSuccess: false },
  submit: vi.fn(),
  Form: (props: any) => <form {...props} />
}

vi.mock('react-router', async () => {
  const original = await vi.importActual('react-router')
  return {
    ...original,
    useNavigate: () => navigate,
    useFetcher: () => fetcher,
    Link: original.Link
  }
})

vi.mock('react-i18next', async () => {
  const original = await vi.importActual('react-i18next')

  return {
    ...original,
    useTranslation: () => ({
      t: (key: string) => key,
      i18n: {
        changeLanguage: () => new Promise(() => null)
      }
    })
  }
})

const routes = [
  {
    path: '/login',
    element: <></>
  },
  {
    path: '/forgot-password',
    element: <ForgotPassword />
  }
]

const makeSut = async () => {
  const router = createMemoryRouter(routes, {
    initialEntries: ['/forgot-password']
  })
  render(<RouterProvider router={router} />)

  return {
    emailInput: await screen.findByPlaceholderText('placeholder_field_email'),
    sendButton: await screen.findByText('btn_send'),
    backToLoginButton: await screen.findByText('btn_back_to_login')
  }
}

describe('ResetPasswordForm', () => {
  it('Should show invalid email validation error', async () => {
    const invalidEmail = 'invalid-email'

    const { emailInput, sendButton } = await makeSut()

    await userEvent.click(emailInput)
    await userEvent.type(emailInput, invalidEmail)

    expect(sendButton).toBeEnabled()
  })

  it('Should show max 300 characters on emailInput', async () => {
    const { emailInput, sendButton } = await makeSut()
    const invalidEmailWithMaxCharacters = 'eMail123W2W1@&.com'

    await userEvent.type(emailInput, invalidEmailWithMaxCharacters)
    await userEvent.tab()

    expect(emailInput).toHaveValue(invalidEmailWithMaxCharacters.slice(0, 300))
    expect(sendButton).toBeEnabled()
  })

  it('Should redirect to login page on click', async () => {
    const { backToLoginButton } = await makeSut()

    fireEvent.click(backToLoginButton)

    expect(navigate).toHaveBeenCalledWith('/login')
  })

  it('Should redirect to login page on click when email was sent', async () => {
    fetcher.data.isSuccess = true
    render(
      <MemoryRouter>
        <ForgotPassword />
      </MemoryRouter>
    )
    const backToLoginButton = screen.getByText('btn_back_to_login')

    fireEvent.click(backToLoginButton)

    expect(navigate).toHaveBeenCalledWith('/login')
  })
})
