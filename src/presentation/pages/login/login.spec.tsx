import { screen, render, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { RouterProvider, createMemoryRouter } from 'react-router'
import { Suspense } from 'react'

import Login from './login'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import type { BadgeProps } from 'softo-design-system'

// Mock das deendências
vi.mock('react-hook-form', () => ({
  useForm: () => ({
    register: vi.fn(),
    handleSubmit: (fn: any) => (e: any) => {
      e.preventDefault()
      fn({ email: '<EMAIL>', password: 'password123' })
    },
    formState: { isValid: true, errors: {} },
    watch: vi.fn()
  })
}))

vi.mock('react-router', async () => {
  const original = await vi.importActual('react-router')
  return {
    ...original,
    useFetcher: () => ({
      state: 'idle',
      submit: vi.fn(),
      data: { error: null },
      Form: (props: any) => <form {...props} />
    }),
    Link: original.Link
  }
})

vi.mock('./validations/login-form-validation', () => ({
  validationSchema: {
    parse: (data: any) => data // Simula a validação bem-sucedida
  }
}))

vi.mock('~/presentation/hooks', () => ({
  useTranslation: () => ({
    translate: (key: any) => key
  })
}))

vi.mock('softo-design-system', async () => {
  const actual = await vi.importActual('softo-design-system')
  return {
    ...actual,
    InputMessage: (props: any) => <div {...props} />,
    Button: (props: any) => <button {...props} />,
    Input: (props: any) => <input {...props} />,
    Badge: ({ children, variant, className, ...props }: BadgeProps) => (
      <div data-testid='mock-badge' className={className} {...props}>
        {children}
      </div>
    )
  }
})

vi.mock('~/presentation/components/spinner/spinner', () => ({
  Spinner: () => <div>Loading...</div>
}))

vi.mock('~/presentation/components/system-version/system-version', () => ({
  SystemVersion: () => <div>System Version</div>
}))

const routes = [
  {
    path: '/login',
    element: (
      <Suspense>
        <Login />
      </Suspense>
    )
  },
  {
    path: '/forgot-password',
    element: <div>Forgot Password</div>
  },
  {
    path: '/register',
    element: <div>Register</div>
  }
]

const makeSut = async () => {
  const router = createMemoryRouter(routes, {
    initialEntries: ['/login']
  })

  const queryClient = new QueryClient()

  // Wrap the router with QueryClientProvider
  render(
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>
  )

  return {
    emailInput: await screen.findByPlaceholderText('placeholder_email'),
    passwordInput: await screen.findByPlaceholderText('placeholder_password'),
    accessButton: await screen.findByText('btn_login'),
    forgotPasswordButton: await screen.findByText('btn_forgot_password'),
    registerButton: await screen.findByText('btn_register')
  }
}

describe('Login', () => {
  it('should display form fields and buttons', async () => {
    const {
      emailInput,
      passwordInput,
      accessButton,
      forgotPasswordButton,
      registerButton
    } = await makeSut()

    expect(emailInput).toBeInTheDocument()
    expect(passwordInput).toBeInTheDocument()
    expect(accessButton).toBeInTheDocument()
    expect(forgotPasswordButton).toBeInTheDocument()
    expect(registerButton).toBeInTheDocument()
  })

  it('should navigate to forgot password page on button click', async () => {
    const { forgotPasswordButton } = await makeSut()

    await userEvent.click(forgotPasswordButton)

    await waitFor(() => {
      expect(screen.getByText('Forgot Password')).toBeInTheDocument()
    })
  })

  it('should navigate to register page on button click', async () => {
    const { registerButton } = await makeSut()

    await userEvent.click(registerButton)

    await waitFor(() => {
      expect(screen.getByText('Register')).toBeInTheDocument()
    })
  })
})
