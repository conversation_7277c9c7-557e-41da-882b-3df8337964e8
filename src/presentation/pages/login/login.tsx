import { useForm } from 'react-hook-form'
import { Link, useFetcher } from 'react-router'
import { isEmpty } from 'lodash'
import { zodResolver } from '@hookform/resolvers/zod'

import { type LoginForm as Model } from '~/domain/models'
import { validationSchema } from './validations/login-form-validation'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { SystemVersion } from '~/presentation/components/system-version/system-version'
import { TextInput } from '~/presentation/components'

const Login = () => {
  const { translate } = useTranslation('login')

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<Model>({
    resolver: zodResolver(validationSchema),
    mode: 'onChange'
  })

  const fetcher = useFetcher()
  const hasErrors = !isEmpty(errors)

  const isLoadingSubmitForm =
    fetcher.state === 'loading' || fetcher.state === 'submitting'

  const onSubmit = (data: Model) => {
    fetcher.submit(data, { method: 'post' })
  }

  return (
    <fetcher.Form
      className='max-w-96 w-full mx-auto mb:pb-10 pb-4'
      onSubmit={handleSubmit(onSubmit)}
    >
      <h1 className='flex justify-center w-full align-center uppercase text-2xl md:text-[1.75rem] font-bold text-center md:mb-16 mb-6'>
        {translate('login_title')}
      </h1>
      <TextInput
        color='secondary'
        label={translate('label_email')}
        placeholder={translate('placeholder_email')}
        maxLength={300}
        error={errors?.email}
        id='email'
        autoComplete='email'
        {...register('email')}
      />
      <TextInput
        color='secondary'
        label={translate('label_password')}
        placeholder={translate('placeholder_password')}
        error={errors?.password}
        id='password'
        wrapperClassname={hasErrors ? 'mb-3' : 'mb-6'}
        autoComplete='current-password'
        isPassword
        {...register('password')}
      />
{/* 
      {fetcher?.data?.error && (
        <InputMessage
          variant='error'
          className='mb-6 text-left justify-start'
          data-testid='message-errors-login'
        >
          {translate('message_errors_login')}
        </InputMessage>
      )} */}

      <div>
        {/* <Button
          disabled={isLoadingSubmitForm}
          type='submit'
          data-testid='access-button'
          className='uppercase w-full mb-4'
        >
          {isLoadingSubmitForm ? <Spinner /> : translate('btn_login')}
        </Button> */}
      </div>

      <div className='w-full grid grid-cols-2 gap-4'>
        <Link to="">
        aa
          {/* <Button
            type='button'
            data-testid='forgot-password-button'
            variant='borderless'
            className='w-full uppercase'
          >
            {translate('btn_forgot_password')}
          </Button> */}
        </Link>
        <Link to="">
        aa
          {/* <Button
            type='button'
            data-testid='register-button'
            className='w-full uppercase'
            variant='ghost'
          >
            {translate('btn_register')}
          </Button> */}
        </Link>
      </div>

      <SystemVersion
        stylesTextVersions='font-semibold'
        stylesContainer='flex flex-col gap-2 w-full justify-center items-center mt-6 md:mt-20'
        stylesContainerVersions='flex flex-col gap-2'
      /> 
    </fetcher.Form>
  )
}

export default Login
