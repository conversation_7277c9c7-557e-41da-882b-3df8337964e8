import { redirect, type ActionFunctionArgs, data } from 'react-router'

import type { QueryClient } from '@tanstack/react-query'
import { userLoginQuery } from '~/presentation/querys/user-login-query'

export const loginAction =
  (queryClient: QueryClient) =>
  async ({ request }: ActionFunctionArgs) => {
    const formData = await request.formData()
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    try {
      await queryClient.ensureQueryData(
        userLoginQuery({
          email,
          password
        })
      )

      return redirect('/')
    } catch (error: any) {
      return data({ error: error.message }, { status: 400 })
    }
  }
