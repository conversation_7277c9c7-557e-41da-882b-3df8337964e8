import { type ReactNode } from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { MemoryRouter } from 'react-router'
import PermissionsProfile from './permissions-profile'

vi.mock('softo-design-system', async () => {
  const actual = await vi.importActual('softo-design-system')
  return {
    ...actual,
    Breadcrumbs: ({ items }: { items: any[] }) => (
      <div data-testid='mock-breadcrumbs'>
        {items.map((item, index) => (
          <span key={index}>{item.link}</span>
        ))}
      </div>
    )
  }
})

vi.mock('~/presentation/hooks', async () => {
  const actual = await vi.importActual('~/presentation/hooks')
  return {
    ...actual,
    useTranslation: () => ({
      translate: (key: string) => key
    }),
    useMediaQuery: () => false
    // your mocked methods
  }
})

vi.mock('~/main/config/query/queryClient', () => {
  return {
    queryClient: {
      // Mock de métodos que você pode precisar
      clear: vi.fn(),
      setQueryData: vi.fn(),
      getQueryData: vi.fn()
      // Adicione outros métodos conforme necessário
    }
  }
})

vi.mock('react-router', async () => {
  const actual = await vi.importActual('react-router')
  return {
    ...actual,
    useFetcher: () => ({
      Form: ({ children }: { children: React.ReactNode }) => (
        <form>{children}</form>
      ),
      state: 'idle'
    })
  }
})

// Mock the useQuery hook
vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual('@tanstack/react-query')
  return {
    ...actual
  }
})

vi.mock('./components/drawer-create-profile/drawer-create-profile', () => {
  return {
    DrawerCreateProfile: ({ onClose, open }: any) => (
      <div>
        {open && (
          <div>
            <span>Mocked DrawerEditProfile</span>
            <button onClick={onClose}>Close</button>
            <div>Profile Create Title</div>
          </div>
        )}
      </div>
    )
  }
})

describe('PermissionsProfile', () => {
  const queryClient = new QueryClient()

  beforeAll(() => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn()
      }))
    })
  })

  const renderWithQueryClient = (ui: ReactNode) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <MemoryRouter>{ui}</MemoryRouter>
      </QueryClientProvider>
    )
  }

  it('renders the page title', async () => {
    renderWithQueryClient(<PermissionsProfile />)
    expect(await screen.findByText(/page_title/i)).toBeInTheDocument()
  })

  it('should opens and closes the create profile drawer', async () => {
    renderWithQueryClient(<PermissionsProfile />)

    const button = await screen.findByRole('button', {
      name: /btn_create_profile/i
    })
    fireEvent.click(button)

    expect(await screen.findByText('Profile Create Title')).toBeInTheDocument()

    const buttonCancel = await screen.findByText('Close')

    fireEvent.click(buttonCancel)

    const titleAfterClose = screen.queryByText('Profile Create Title')

    expect(titleAfterClose).not.toBeInTheDocument()
  })
})
