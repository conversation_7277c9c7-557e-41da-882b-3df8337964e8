import { <PERSON><PERSON>ead, TableHeader, TableRow } from 'softo-design-system'
import { useTranslation } from '~/presentation/hooks/use-translation'

export const DataGridHead = () => {
  const { translate } = useTranslation('permissions-profile')

  return (
    <TableHeader className='h-12 border-palettes-neutral-80 block'>
      <TableRow className='grid grid-cols-[90%_10%] md:grid-cols-[60%_30%_10%] lg:grid-cols-[75%_20%_5%] items-center'>
        <TableHead className='w-full pl-4'>{translate('label_name')}</TableHead>
        <TableHead className='hidden md:block w-full'>
          {translate('label_default_profile')}
        </TableHead>
        <TableHead className='w-full'></TableHead>
      </TableRow>
    </TableHeader>
  )
}
