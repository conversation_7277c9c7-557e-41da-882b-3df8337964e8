import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { MemoryRouter, Route, Routes } from 'react-router'
import { DataGridRow } from './data-grid-row'
import type { LoadRolesModel } from '~/domain/models/load-roles-model'
import type { ReactNode } from 'react'

const mockProfile: LoadRolesModel = {
  id: '1',
  name: 'Test Profile',
  isUserDefault: false,
  isAdminDefault: false
}

vi.mock('~/presentation/components/three-dots/three-dots', async () => {
  const actual = await vi.importActual(
    '~/presentation/components/three-dots/three-dots'
  )
  return {
    ...actual
  }
})

vi.mock('softo-design-system', async () => {
  const actual = await vi.importActual('softo-design-system')
  return {
    ...actual,
    Badge: vi.fn(({ children, ...props }) => (
      <div data-testid='mock-badge' {...props}>
        {children}
      </div>
    ))
  }
})

//

vi.mock('../../hooks/use-three-dots-options', () => ({
  useThreeDotsOptions: () => ({
    threeDotsOptions: () => [
      {
        name: 'view_profile',
        icon: 'visibility',
        action: vi.fn() // Mock da ação
      },
      {
        name: 'edit_profile',
        icon: 'edit',
        action: vi.fn() // Mock da ação
      }
    ]
  })
}))

vi.mock(
  '~/presentation/pages/permissions-profile-details/components/drawer-edit-profile/drawer-edit-profile',
  () => {
    return {
      DrawerEditProfile: ({ onClose, open, roleName, roleIdParams }: any) => (
        <div>
          {open && (
            <div>
              <span>Mocked DrawerEditProfile</span>
              <button onClick={onClose}>Close</button>
              <div>{roleName}</div>
              <div>{roleIdParams}</div>
            </div>
          )}
        </div>
      )
    }
  }
)

const queryClient = new QueryClient()

const renderWithQueryClient = (ui: React.ReactNode) => {
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter initialEntries={['/permissions-profile']}>
        <Routes>
          <Route path='/permissions-profile' element={ui} />
          <Route
            path='/permissions-profile/:roleId'
            element={<div>Profile Details</div>}
          />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  )
}

vi.mock('softo-design-system', async () => {
  const actual = await vi.importActual('softo-design-system')
  return {
    ...actual,
    DropdownMenu: ({ children }: { children: ReactNode }) => (
      <div>{children}</div>
    ),
    DropdownMenuTrigger: ({ children }: { children: ReactNode }) => (
      <div>{children}</div>
    ),
    DropdownMenuContent: ({ children }: { children: ReactNode }) => (
      <div>{children}</div>
    ),
    DropdownMenuItem: ({ children }: { children: ReactNode }) => (
      <div>{children}</div>
    )
  }
})

describe('DataGridRow', () => {
  it('should renders the profile name', async () => {
    renderWithQueryClient(<DataGridRow profile={mockProfile} index={0} />)
    expect(await screen.findByText(/Test Profile/i)).toBeInTheDocument()
  })

  it('should redirects to the correct route when NavLink is clicked', async () => {
    renderWithQueryClient(<DataGridRow profile={mockProfile} index={0} />)

    const navLink = screen.getByRole('link', { name: /Test Profile/i })
    fireEvent.click(navLink)

    expect(await screen.findByText('Profile Details')).toBeInTheDocument()
  })

  it('should verify element inside Three Dots', async () => {
    renderWithQueryClient(<DataGridRow profile={mockProfile} index={0} />)

    const threeDotsButton = await screen.findByRole('button', {
      name: /more_vert/i
    })
    fireEvent.click(threeDotsButton)

    await waitFor(() => {
      expect(screen.getByText('view_profile')).toBeInTheDocument()
      expect(screen.getByText('edit_profile')).toBeInTheDocument()
    })
  })
})
