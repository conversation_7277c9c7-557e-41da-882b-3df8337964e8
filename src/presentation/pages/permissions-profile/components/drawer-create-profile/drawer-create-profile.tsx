import { useState } from 'react'
import { useFetcher } from 'react-router'

import { ConfirmModal, TextInput } from '~/presentation/components'
import { useToast } from '~/presentation/hooks/use-toast'
import { When } from '~/presentation/components/when/when'
import { Spinner } from '~/presentation/components/spinner/spinner'
import { validationSchema } from '../../validations/create-profile-validation'
import { zodResolver } from '@hookform/resolvers/zod'
import type { CreateProfileForm } from '~/domain/models'
import { useForm } from 'react-hook-form'
import { useMutation } from '@tanstack/react-query'
import { makeCreateRoleService } from '~/application/usecases/roles/create-role'
import { ToastWrapper } from '~/presentation/components/toast-wrapper/toast-wrapper'
import { queryClient } from '~/main/config/query/queryClient'

import { useTranslation } from 'react-i18next'
import { queryKeyLoadRoles } from '~/presentation/querys/roles/load-roles-query'
import type { isConflictException } from '~/domain/exceptions/is-conflict.exception'

import {
  She<PERSON>,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  SheetT<PERSON>le,
  Button,
  I<PERSON>s
} from 'softo-design-system'

type Props = {
  open: boolean
  onClose: () => void
}

export const DrawerCreateProfile = ({ open, onClose }: Props) => {
  const { t: translate } = useTranslation('create-profile')

  const [isModalOpen, setIsModalOpen] = useState(false)

  const { isToastOpen, showToast, hideToast, toastDescription, toastVariant } =
    useToast()

  const createRoleService = makeCreateRoleService()

  const setOpenModal = () => setIsModalOpen(!isModalOpen)

  const {
    watch,
    register,
    handleSubmit,
    reset,

    formState: { errors, isDirty }
  } = useForm<CreateProfileForm>({
    resolver: zodResolver(validationSchema),
    mode: 'onBlur'
  })

  const fetcher = useFetcher()

  const isLoadingSubmitForm =
    fetcher.state === 'loading' || fetcher.state === 'submitting'

  const onSubmit = (data: CreateProfileForm) => {
    createRole(data)
  }

  const confirmCancel = () => {
    if (isDirty && watch('name') !== '') {
      setOpenModal()
    } else {
      onClose()
      reset()
    }
  }

  const handleCloseDrawer = () => {
    reset()
    onClose()
  }

  const { mutate: createRole } = useMutation({
    mutationFn: (data: CreateProfileForm) => createRoleService.execute(data),
    onSuccess: () => {
      onClose()
      showToast(
        `${translate('create_success_alert_message', { name: watch('name') })}`,
        'success'
      )
      reset()
      queryClient.invalidateQueries({
        queryKey: [queryKeyLoadRoles]
      })
    },
    onError: (error: isConflictException) => {
      onClose()
      reset()
      if (error?.error?.message === 'Role already exists') {
        showToast(translate('message_errors_role_exists'), 'error')
      } else {
        showToast()
      }
    }
  })

  return (
    <>
      <ConfirmModal
        isModalOpen={isModalOpen}
        setOpenModal={setOpenModal}
        handleResetAndClose={handleCloseDrawer}
        title={translate('common:cancel_title')}
        description={translate('cancel_description')}
        btnSecondaryText={translate('common:btn_return')}
        btnPrimaryText={translate('common:btn_confirm')}
      />

      <When condition={isToastOpen}>
        <ToastWrapper
          toastVariant={toastVariant}
          description={toastDescription}
          isToastOpen={isToastOpen}
          onClose={hideToast}
        />
      </When>
      <Sheet open={open}>
        <SheetContent className='bg-white max-h-full min-h-full rounded-none xs:w-[100vw] w-full sm:max-w-[720px] sm:w-[720px] md:w-[720px] p-0'>
          <SheetHeader className='w-full p-6 flex justify-end items-end border-b'>
            <Button
              onClick={confirmCancel}
              variant='ghost'
              className='w-12 h-12'
            >
              <Icons icon='close' wrapperClassname='flex items-center' />
            </Button>
          </SheetHeader>
          <div className='flex flex-col items-center w-full px-6 md:px-0 md:max-w-[720px] mx-auto h-screen overflow-y-auto overflow-x-hidden py-20 md:py-28 bg-white'>
            <form
              className='w-full h-full md:w-[720px] flex bg-white'
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className='flex flex-col items-center max-w-[440px] mx-auto w-full'>
                <SheetTitle className='text-[28px] uppercase text-black font-bold'>
                  {translate('profile_creation_title')}
                </SheetTitle>
                <TextInput
                  label={translate('label_profile')}
                  placeholder={translate('placeholder_profile')}
                  maxLength={100}
                  id='name'
                  wrapperClassname='w-full mt-16'
                  error={errors?.name}
                  {...register('name')}
                />
                <div className='flex gap-5 w-full'>
                  <Button
                    size='lg'
                    type='button'
                    variant='ghost'
                    className='w-[211px] uppercase'
                    onClick={confirmCancel}
                  >
                    {translate('btn_cancel')}
                  </Button>
                  <Button
                    size='lg'
                    type='submit'
                    variant='default'
                    className='w-[211px] uppercase'
                  >
                    {isLoadingSubmitForm ? (
                      <Spinner />
                    ) : (
                      translate('btn_create')
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
}
