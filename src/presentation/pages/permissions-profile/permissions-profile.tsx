import {
  Breadcrumbs,
  type Bread<PERSON>rum<PERSON><PERSON>temProps,
  Button,
  Icons
} from 'softo-design-system'
import { NavLink, useNavigate } from 'react-router'
import { ROUTES } from '~/main/types'
import { useQuery } from '@tanstack/react-query'
import { PageTitle } from '~/presentation/components/page-title/page-title'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { DataGrid } from '~/presentation/components/data-grid/data-grid'
import { DataGridHead } from './components/data-grid-head/data-grid-head'
import { DataGridRow } from './components/data-grid-row/data-grid-row'
import type { LoadRoles } from '~/application/usecases/roles/load-roles'
import { useEffect, useState } from 'react'
import { DrawerCreateProfile } from './components/drawer-create-profile/drawer-create-profile'
import { useMediaQuery } from '~/presentation/hooks/use-media-query'
import { loadRolesQuery } from '~/presentation/querys/roles/load-roles-query'
import { loadA<PERSON>untMeQuery } from '~/presentation/querys/load-account-me-query'

const PermissionsProfile = () => {
  const navigate = useNavigate()
  const [isDrawerCreateProfileOpen, setIsDrawerCreateProfileOpen] =
    useState(false)

  const isMobile = useMediaQuery('(max-width:768px)')
  const { translate } = useTranslation('permissions-profile')

  const { data } = useQuery({ ...loadAccountMeQuery() })

  const handleOpenDrawerCreate = () =>
    setIsDrawerCreateProfileOpen((prev) => !prev)

  const { data: roles } = useQuery(loadRolesQuery())

  useEffect(() => {
    if (
      !data?.permissions?.find(
        (permission) => permission.slug === 'view-permission-profile'
      )
    ) {
      navigate(ROUTES.HOME)
    }
  }, [data])

  const breadcrumb: BreadcrumbItemProps[] = [
    {
      link: <NavLink to={ROUTES.HOME}>{translate('breadcrumb_home')}</NavLink>,
      icon: 'home'
    },
    {
      link: (
        <NavLink to={ROUTES.PERMISSIONS_PROFILE}>
          {translate('breadcrumb_profile_permissions')}
        </NavLink>
      )
    }
  ]

  return (
    <section className='px-4 md:px-28 py-10 overflow-auto h-[calc(100vh-104px)] md:h-full'>
      <Breadcrumbs items={breadcrumb} />

      <section className='flex flex-col mt-20 max-md:mt-10 max-md:max-w-full'>
        <div className='flex' id='wrapper'>
          <PageTitle title={translate('page_title')} />
          <Button
            variant='default'
            className={isMobile ? 'w-[40px]' : 'w-[211px]'}
            onClick={handleOpenDrawerCreate}
          >
            {isMobile ? (
              <Icons icon='add' wrapperClassname='flex items-center' />
            ) : (
              translate('btn_create_profile')
            )}
          </Button>
        </div>
        <DataGrid<LoadRoles.Response>
          className='mt-10'
          loadQuery={loadRolesQuery}
          dataGridHead={<DataGridHead />}
        >
          {roles?.rows?.map((profile, index) => (
            <DataGridRow key={profile.id} profile={profile} index={index} />
          ))}
        </DataGrid>
        <DrawerCreateProfile
          open={isDrawerCreateProfileOpen}
          onClose={handleOpenDrawerCreate}
        />
      </section>
    </section>
  )
}

export default PermissionsProfile
