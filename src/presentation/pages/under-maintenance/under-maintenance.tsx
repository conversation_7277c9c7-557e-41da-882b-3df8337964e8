import { Image } from '~/presentation/components/image/image'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { useBucketImage } from '~/presentation/hooks/use-bucket-image'

const UnderMaintenance = () => {
  const { translate } = useTranslation('under-maintenance')
  const imageUrl = useBucketImage('astronaut-maintenance.png')

  return (
    <div className='flex flex-col items-center justify-center w-full h-full bg-white'>
      <h1 className='mt-4 text-[57px] font-bold text-center uppercase max-w-[620px]'>
        {translate('page_title')}
      </h1>
      <span className='block my-4 text-center text-base mb-12'>
        {translate('page_description')}
      </span>
      <Image
        src={imageUrl}
        width={528}
        height={424}
        alt={translate('alt_image_description')}
      />
    </div>
  )
}

export default UnderMaintenance
