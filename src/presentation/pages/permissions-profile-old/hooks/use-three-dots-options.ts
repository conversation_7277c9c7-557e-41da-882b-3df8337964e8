import { useNavigate } from 'react-router'
import { ROUTES } from '~/main/types'
import type { ThreeDotsOption } from '~/presentation/components/three-dots/three-dots'

type Props = {
  onOpenEditProfile: () => void
}

export const useThreeDotsOptions = ({ onOpenEditProfile }: Props) => {
  const navigate = useNavigate()

  const threeDotsOptions = (profileId: string): ThreeDotsOption[] => [
    {
      name: 'view_profile',
      icon: 'visibility',
      action: () => {
        navigate(
          ROUTES.PERMISSIONS_PROFILE_DETAILS.replace(':roleId', profileId)
        )
      }
    },
    {
      name: 'edit_profile',
      icon: 'edit',
      action: () => onOpenEditProfile()
    }
  ]

  return {
    threeDotsOptions
  }
}
