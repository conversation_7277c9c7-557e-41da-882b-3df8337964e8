import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { DrawerCreateProfile } from './drawer-create-profile'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useMutation } from '@tanstack/react-query'

type SheetProps = {
  children: React.ReactNode
  open: boolean
}

type SheetContentProps = {
  children: React.ReactNode
}

vi.mock('softo-design-system', async () => {
  const actual = await vi.importActual<any>('softo-design-system')
  return {
    ...actual,
    Sheet: ({ children, open }: SheetProps) =>
      open ? <div data-testid='mock-drawer'>{children}</div> : null,
    SheetContent: ({ children }: SheetContentProps) => (
      <div data-testid='mock-drawer-content'>{children}</div>
    ),
    SheetHeader: ({ children }: SheetContentProps) => (
      <div data-testid='mock-drawer-header'>{children}</div>
    ),
    SheetTitle: ({ children }: SheetContentProps) => (
      <div data-testid='mock-drawer-title'>{children}</div>
    )
  }
})

vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual<any>('@tanstack/react-query')

  return {
    ...actual,
    useMutation: vi.fn(() => ({
      mutate: vi.fn(),
      isLoading: false
    }))
  }
})

vi.mock('~/application/usecases/roles/create-role', () => ({
  makeCreateRoleService: vi.fn(() => ({
    execute: vi.fn()
  }))
}))

vi.mock('react-router', () => ({
  useFetcher: () => ({
    Form: ({ children }: { children: React.ReactNode }) => (
      <form>{children}</form>
    ),
    state: 'idle'
  })
}))

vi.mock('~/presentation/hooks', () => ({
  useToast: () => ({
    isToastOpen: false,
    toggleToast: vi.fn(),
    toastDescription: '',
    setToastDescription: vi.fn(),
    toastVariant: 'success',
    setToastVariant: vi.fn()
  })
}))

vi.mock('react-i18next', async () => {
  const original = await vi.importActual('react-i18next')

  return {
    ...original,
    useTranslation: () => ({
      t: (key: string) => key,
      i18n: {
        changeLanguage: () => new Promise(() => null)
      }
    })
  }
})

vi.mock('~/presentation/hooks', async () => {
  const actual = await vi.importActual('~/presentation/hooks')
  return {
    ...actual
    // your mocked methods
  }
})

describe('DrawerCreateProfile', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useMutation as ReturnType<typeof vi.fn>).mockReturnValue({
      mutate: vi.fn(),
      isLoading: false
    })
  })

  it('renders the drawer when open is true', async () => {
    render(<DrawerCreateProfile {...defaultProps} />)
    expect(await screen.findByTestId('mock-drawer')).toBeInTheDocument()
    expect(
      await screen.findByText('profile_creation_title')
    ).toBeInTheDocument()
  })

  it('does not render the drawer when open is false', () => {
    render(<DrawerCreateProfile {...defaultProps} open={false} />)
    expect(screen.queryByTestId('mock-drawer')).not.toBeInTheDocument()
  })

  it('calls onClose when close button is clicked', () => {
    render(<DrawerCreateProfile {...defaultProps} />)
    fireEvent.click(screen.getByRole('button', { name: /close/i }))
    expect(defaultProps.onClose).toHaveBeenCalled()
  })

  it('displays correct title', () => {
    render(<DrawerCreateProfile {...defaultProps} />)
    expect(screen.getByText('profile_creation_title')).toBeInTheDocument()
  })
})
