import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { DataGridHead } from './data-grid-head'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { MemoryRouter } from 'react-router'

const renderWithQueryClient = (ui: React.ReactNode) => {
  const queryClient = new QueryClient()
  return render(
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>{ui}</MemoryRouter>
    </QueryClientProvider>
  )
}

describe('DataGridHead', () => {
  it('renders the correct header texts', () => {
    renderWithQueryClient(<DataGridHead />)

    expect(screen.getByText(/label_name/i)).toBeInTheDocument()
    expect(screen.getByText(/label_default_profile/i)).toBeInTheDocument()
  })
})
