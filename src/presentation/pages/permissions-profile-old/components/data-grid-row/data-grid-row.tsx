import { useState } from 'react'
import { Badge, TableCell, TableRow } from 'softo-design-system'
import { NavLink } from 'react-router'
import { ThreeDots } from '~/presentation/components/three-dots/three-dots'
import { When } from '~/presentation/components/when/when'
import { useThreeDotsOptions } from '../../hooks/use-three-dots-options'
import { ROUTES } from '~/main/types'
import type { LoadRolesModel } from '~/domain/models/load-roles-model'
import { DrawerEditProfile } from '~/presentation/pages/permissions-profile-details/components/drawer-edit-profile/drawer-edit-profile'

type Props = {
  profile: LoadRolesModel
  index: number
}

export const DataGridRow = ({ profile, index }: Props) => {
  const [openEditProfile, setOpenEditProfile] = useState(false)

  const onToggleEditProfile = () => setOpenEditProfile((prev) => !prev)

  const { threeDotsOptions } = useThreeDotsOptions({
    onOpenEditProfile: onToggleEditProfile
  })

  return (
    <TableRow
      className={`h-16 border-0 grid grid-cols-[90%_10%] md:grid-cols-[60%_30%_10%] lg:grid-cols-[75%_20%_5%] items-center w-full ${index % 2 === 0 ? 'bg-white' : 'bg-background-table-row'}`}
    >
      <TableCell className='font-semibold text-text-icon-high-emphasis cursor-pointer'>
        <NavLink
          className='w-full p-2 gap-4 md:gap-0 flex'
          to={ROUTES.PERMISSIONS_PROFILE_DETAILS.replace(':roleId', profile.id)}
        >
          {profile.name}
          <When condition={profile.isUserDefault || profile.isAdminDefault}>
            <Badge
              className='md:hidden uppercase min-w-max px-2'
              variant='outline'
            >
              {profile.isUserDefault ? 'User' : 'Admin'} default
            </Badge>
          </When>
        </NavLink>
      </TableCell>

      <TableCell className='hidden md:block w-full'>
        <When condition={profile.isUserDefault || profile.isAdminDefault}>
          <Badge className='uppercase min-w-max px-2' variant='outline'>
            {profile.isUserDefault ? 'User' : 'Admin'} default
          </Badge>
        </When>
      </TableCell>
      <TableCell className='w-full'>
        <>
          <ThreeDots
            options={threeDotsOptions(profile.id)}
            i18nKey='permissions-profile'
          />
          <DrawerEditProfile
            onClose={onToggleEditProfile}
            open={openEditProfile}
            roleName={profile?.name}
            roleIdParams={profile?.id}
          />
        </>
      </TableCell>
    </TableRow>
  )
}
