import { z } from 'zod'

export const normalizeSlug = (value: string) => {
  return (
    value
      ?.toLowerCase() // Converter para minúsculas
      ?.normalize('NFD') // Normalizar para decompor caracteres acentuados
      // biome-ignore lint/suspicious/noMisleadingCharacterClass: Remover marcas diacríticas (acentos)
      ?.replace(/[\u0300-\u036f]/g, '') // Remover marcas diacríticas (acentos)
      ?.replace(/ç/g, 'c') // Substituir ç por c
      ?.replace(/[^a-z0-9]/g, '') // Remover caracteres especiais e espaços
      ?.slice(0, 100)
  )
}

export const validationSchema = z
  .object({
    tenant: z
      .object({
        name: z
          .string()
          .min(1, 'validation_company_name_required')
          .max(100, 'validation_max_length_100')
          .regex(/^[^\s*$]/, 'validation_company_name_white_spaces'),
        slug: z
          .string()
          .min(1, 'validation_slug_required')
          .max(58, 'validation_max_length_58')
      })
      .refine(
        (tenant) => {
          const parts = tenant.slug.split('/')
          const slugPart = parts[parts.length - 1]
          return slugPart.length >= 1 && slugPart.length <= 100
        },
        {
          message: 'validation_slug_required',
          path: ['slug']
        }
      ),
    admin: z.object({
      fullName: z
        .string()
        .min(1, 'validation_fullname_required')
        .max(100, 'validation_max_length_200')
        .regex(
          /^(?=.*\s)(?=.*[A-Za-zÀ-ÿ])[A-Za-zÀ-ÿ\s.,;:!?'-]*[A-Za-zÀ-ÿ]+$/,
          'validation_fullname_compound_name'
        )
        .regex(/^[^\s*$]/, 'validation_fullname_white_spaces'),
      email: z
        .string()
        .min(1, 'validation_email_required')
        .max(100)
        .regex(
          /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
          'validation_email_invalid'
        ),
      password: z.string().min(1, 'validation_password_required').max(100),
      passwordConfirmation: z
        .string()
        .min(1, 'validation_confirm_password_required')
        .max(100)
    })
  })
  .superRefine(({ admin }, ctx) => {
    if (admin.passwordConfirmation !== admin.password) {
      ctx.addIssue({
        code: 'custom',
        message: 'validation_password_confirm',
        path: ['admin.passwordConfirmation']
      })
    }
  })
