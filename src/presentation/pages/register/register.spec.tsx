import { screen, render, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import RegisterForm from './register'
import { vi } from 'vitest'
import { type ReactNode } from 'react'
import { ROUTES } from '~/main/types'
import { RouterProvider, createMemoryRouter } from 'react-router'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Mock do hook useNavigate
const navigate = vi.fn()
const mockSubmit = vi.fn((formData, options) => {
  console.log('submit called with:', formData, options)
  return Promise.resolve()
})

vi.mock('@tanstack/react-query', async () => {
  const originalModule = await vi.importActual('@tanstack/react-query')
  return {
    ...originalModule
  }
})

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: () => new Promise(() => null)
    }
  })
}))

vi.mock('react-router', async () => {
  const original =
    await vi.importActual<typeof import('react-router')>('react-router')
  return {
    ...original,
    useNavigate: () => navigate,
    useFetcher: () => ({
      state: 'idle',
      data: { isSuccess: false, error: null },
      submit: mockSubmit,
      Form: (props: any) => <form {...props} />
    }),
    Link: original.Link,
    Form: original.Form
  }
})

// Mock do TextInput
vi.mock('~/presentation/components/text-input', () => ({
  TextInput: ({
    name,
    children,
    dataTestId,
    success,
    labelProps,
    isPassword,
    inputAdornment,
    ...props
  }: {
    name: string
    dataTestId: string
    children: ReactNode
    success: boolean
    isPassword: boolean
    inputAdornment: any
    labelProps: { content: string }
  }) => (
    <>
      <input type='text' name={name} data-testid={dataTestId} {...props} />
      {children}
    </>
  )
}))

// Mock do PasswordRules
vi.mock('~/presentation/components/password-rules/password-rules', () => ({
  PasswordRules: () => (
    <div data-testid='password-rules'>Mocked PasswordRules</div>
  )
}))

const routes = [
  {
    path: '/login',
    element: <div>Login Page</div> // Mock da página de login
  },
  {
    path: '/register',
    element: <RegisterForm />
  }
]

vi.mock('~/presentation/hooks/use-media-query', () => ({
  useMediaQuery: (query: string) => {
    if (query === '(max-width:768px)') {
      return true // Simula que está em uma tela pequena
    }
    return false
  }
}))

const makeSut = async () => {
  const router = createMemoryRouter(routes, {
    initialEntries: ['/register'],
    initialIndex: 0
  })

  const queryClient = new QueryClient()

  // Wrap the router with QueryClientProvider
  render(
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>
  )

  const tenantNameInput = await screen.findByPlaceholderText(
    'placeholder_company_name'
  )
  const slugNameInput = await screen.findByDisplayValue('http://')
  const fullNameInput = await screen.findByPlaceholderText(
    'placeholder_user_fullname'
  )
  const emailInput = await screen.findByPlaceholderText(
    'placeholder_user_email'
  )
  const passwordInput = await screen.findByPlaceholderText(
    'placeholder_user_password'
  )
  const passwordConfirmationInput = await screen.findByPlaceholderText(
    'placeholder_confirm_password'
  )
  const submitButton = await screen.findByText('btn_create_register')
  const backToLoginButton = await screen.findByText('btn_cancel')

  return {
    fullNameInput,
    emailInput,
    slugNameInput,
    tenantNameInput,
    passwordInput,
    passwordConfirmationInput,
    submitButton,
    backToLoginButton,
    router,
    fetcher: {
      submit: mockSubmit
    } // Mock do fetcher para ser acessível
  }
}

describe('RegisterForm', () => {
  it('Should show max length input values', async () => {
    const { fullNameInput, emailInput, submitButton } = await makeSut()

    const maxLengthFullName = 'a'.repeat(101)
    const invalidMaxLengthEmail = 'c'.repeat(101)

    await userEvent.type(fullNameInput, maxLengthFullName)
    await userEvent.type(emailInput, invalidMaxLengthEmail)
    await userEvent.tab()

    waitFor(
      () => {
        expect(fullNameInput).toHaveValue(maxLengthFullName.substring(0, 100))
        expect(emailInput).toHaveValue(invalidMaxLengthEmail.substring(0, 100))
        expect(submitButton).toBeDisabled()
      },
      { timeout: 4000 }
    )
  })

  it('Should redirect to login page on click', async () => {
    const { backToLoginButton, router } = await makeSut()

    await userEvent.click(backToLoginButton)

    expect(router.state.location.pathname).toBe(ROUTES.LOGIN)
  })

  it('Should call service with correct values', async () => {
    const {
      emailInput,
      fullNameInput,
      slugNameInput,
      tenantNameInput,
      passwordInput,
      passwordConfirmationInput,
      submitButton
    } = await makeSut()

    const email = '<EMAIL>'
    const fullName = 'John Doe'
    const password = '@Senha123'
    const tenantName = 'Softo'
    const slug = 'softo'

    await userEvent.type(tenantNameInput, tenantName)
    await userEvent.type(slugNameInput, slug)
    await userEvent.type(emailInput, email)
    await userEvent.type(fullNameInput, fullName)
    await userEvent.type(passwordInput, password)
    await userEvent.type(passwordConfirmationInput, password)

    await userEvent.click(submitButton)

    //FIXME: Fix spy error when submit form
    // await waitFor(() => {
    // 	expect(fetcher.submit).toHaveBeenCalledWith(expect.any(FormData), {
    // 		method: "POST"
    // 	})

    // 	// Validar o conteúdo do FormData
    // 	const formDataArg = fetcher.submit.mock.calls[0][0] as FormData
    // 	expect(formDataArg.get("admin.email")).toBe(email)
    // 	expect(formDataArg.get("admin.fullName")).toBe(fullName)
    // 	expect(formDataArg.get("admin.password")).toBe(password)
    // 	expect(formDataArg.get("admin.passwordConfirmation")).toBe(password)
    // 	expect(formDataArg.get("tenant.name")).toBe(tenantName)
    // 	expect(formDataArg.get("tenant.slug")).toBe(
    // 		`http://${slug.toLowerCase()}`
    // 	)
    // })
  })
})
