import { useEffect, useState, type ChangeEvent } from 'react'
import { Link } from 'react-router'
import { useForm } from 'react-hook-form'
import { ArrowLeftIcon } from '@radix-ui/react-icons'
import { zodResolver } from '@hookform/resolvers/zod'
import { ROUTES } from '~/main/types'
import {
  validationSchema,
  normalizeSlug
} from '../../validation/use-register-form-validation'
import { useFetcher } from 'react-router'
import { validatePassword } from '~/presentation/utils/validate-password'
import { Badge, InputMessage, Button } from 'softo-design-system'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { PasswordRules } from '~/presentation/components/password-rules/password-rules'
import type { TenantRegisterModel } from '~/domain/models/tenant-register-model'
import { Spinner } from '~/presentation/components/spinner/spinner'
import { When } from '~/presentation/components/when/when'
import { SuccessRegister } from '../success-register/success-register'
import { SystemVersion } from '~/presentation/components/system-version/system-version'

import { TextInput } from '~/presentation/components/text-input'
import { useMediaQuery } from '~/presentation/hooks/use-media-query'

export const RegisterForm = () => {
  const { translate, language } = useTranslation('sign-up')
  const isMobile = useMediaQuery('(max-width:768px)')

  const fetcher = useFetcher()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    setValue,
    watch
  } = useForm<TenantRegisterModel>({
    resolver: zodResolver(validationSchema),
    mode: 'onBlur'
  })

  const password = watch('admin.password')
  const companyName = watch('tenant.name')

  const [slug, setSlug] = useState(`http://${normalizeSlug(companyName ?? '')}`)

  const { isValid: isValidPassword } = validatePassword({ password })

  const isLoadingSubmitForm =
    fetcher.state === 'loading' || fetcher.state === 'submitting'

  const disabledSentButton = isLoadingSubmitForm

  useEffect(() => {
    setSlug(`http://${normalizeSlug(companyName?.slice(0, 51) ?? '')}`)
    setValue(
      'tenant.slug',
      `http://${normalizeSlug(companyName?.slice(0, 51) ?? '')}`
    )
  }, [companyName])

  const handleFormatSlug = (e: ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value

    // If the input value is less than the prefix length, reset to the prefix
    if (inputValue.length < 7) {
      setSlug('http://')
      setValue('tenant.slug', 'http://')
      return
    }

    const cleanedInput = inputValue.startsWith('http://')
      ? inputValue.slice(7)
      : inputValue

    const transformedInput = normalizeSlug(cleanedInput)
    const newSlug = `http://${transformedInput}`

    // Only update state if the new slug doesn't duplicate the prefix
    if (newSlug !== slug) {
      setSlug(newSlug)
      setValue('tenant.slug', newSlug)
    }
  }

  const onSubmit = (data: TenantRegisterModel) => {
    if (!isValidPassword) return

    const formData = new FormData()

    const adminData = { ...data.admin, language: language }
    formData.append('admin', JSON.stringify(adminData))

    for (const key in data) {
      const value = data[key as keyof TenantRegisterModel]
      if (key !== 'admin') {
        if (typeof value === 'object' && value !== null) {
          formData.append(key, JSON.stringify(value))
        } else {
          formData.append(key, value as string)
        }
      }
    }

    fetcher.submit(formData, { method: 'POST' })
  }

  useEffect(() => {
    if (fetcher?.data?.error === 'Tenant slug already exists') {
      setError('tenant.slug', {
        message: 'common:errors.tenant_slug_already_exists'
      })
    }
  }, [fetcher?.data?.error])

  return (
    <div className='flex flex-col pb-6 md:pb-0'>
      <When condition={fetcher?.data?.isSuccess}>
        <SuccessRegister />
      </When>
      <When condition={!fetcher?.data?.isSuccess}>
        <fetcher.Form
          className='flex flex-col max-w-84 w-full'
          onSubmit={handleSubmit(onSubmit)}
        >
          <When condition={isMobile}>
            <Link to={ROUTES.LOGIN} className='w-max mx-auto mb-8'>
              <Button variant='borderless' className='gap-2 uppercase'>
                <ArrowLeftIcon /> Retornar ao login
              </Button>
            </Link>
          </When>

          <h1 className='flex justify-center w-full align-center uppercase text-2xl font-bold text-center mb-12 md:mb-16'>
            {translate('sign_up_title')}
          </h1>
          <div>
            <h3 className='uppercase text-sm md:text-md mb-5 font-medium'>
              {translate('title_company_data')}
            </h3>
            <TextInput
              {...register('tenant.name')}
              error={errors?.tenant?.name}
              label={translate('label_company_name')}
              placeholder={translate('placeholder_company_name')}
              maxLength={100}
            />
            <TextInput
              {...register('tenant.slug')}
              label={translate('label_slug')}
              onChange={handleFormatSlug}
              error={errors?.tenant?.slug}
              value={slug}
              className='pr-28'
              maxLength={58}
              inputAdornment={{
                right: (
                  <Badge variant='inverted' className='w-20 px-2'>
                    .{import.meta.env.VITE_DOMAIN}
                  </Badge>
                )
              }}
            />
          </div>
          <div className='border-t pt-6 border-palettes-neutral-90'>
            <h3 className='uppercase text-sm md:text-md mb-5 font-medium'>
              {translate('title_user_data')}
            </h3>
            <TextInput
              {...register('admin.fullName')}
              error={errors?.admin?.fullName}
              label={translate('label_user_fullname')}
              placeholder={translate('placeholder_user_fullname')}
              maxLength={100}
            />
            <TextInput
              {...register('admin.email')}
              error={errors?.admin?.email}
              label={translate('label_user_email')}
              placeholder={translate('placeholder_user_email')}
              maxLength={100}
            />

            <TextInput
              {...register('admin.password')}
              error={errors?.admin?.password}
              label={translate('label_user_password')}
              placeholder={translate('placeholder_user_password')}
              isPassword
              maxLength={100}
            />

            <PasswordRules password={password} />

            <TextInput
              {...register('admin.passwordConfirmation')}
              error={errors?.admin?.passwordConfirmation}
              label={translate('label_confirm_password')}
              placeholder={translate('placeholder_confirm_password')}
              isPassword
              maxLength={100}
            />
          </div>

          <div className='w-full grid grid-cols-2 gap-4'>
            <Link to={ROUTES.LOGIN}>
              <Button
                name='voltar ao login'
                type='button'
                variant='ghost'
                className='md:uppercase w-full'
                size={isMobile ? 'default' : 'lg'}
              >
                {translate('btn_cancel')}
              </Button>
            </Link>
            <Button
              type='submit'
              disabled={disabledSentButton}
              className='md:uppercase'
              size={isMobile ? 'default' : 'lg'}
            >
              {isLoadingSubmitForm ? (
                <Spinner />
              ) : (
                translate('btn_create_register')
              )}
            </Button>
          </div>
        </fetcher.Form>
      </When>
      <SystemVersion
        stylesTextVersions='font-semibold'
        stylesContainer='flex flex-col gap-2 w-full justify-center items-center mt-6 md:mt-15'
        stylesContainerVersions='flex flex-col gap-2'
      />
    </div>
  )
}
