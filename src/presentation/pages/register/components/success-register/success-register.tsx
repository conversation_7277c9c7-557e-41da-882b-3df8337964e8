import { But<PERSON> } from 'softo-design-system'
import { <PERSON> } from 'react-router'
import { ROUTES } from '~/main/types'
import { useTranslation } from '~/presentation/hooks/use-translation'

export const SuccessRegister = () => {
  const { translate } = useTranslation('sign-up')

  return (
    <div className='flex flex-col items-center justify-center pt-12 md:pt-0'>
      <h1 className='text-center text-[28px] font-bold text-text-icon-high-emphasis'>
        {translate('title_success_register')}
      </h1>
      <p className='text-sm mt-4 max-w-96 text-center'>
        {translate('description_success_register')}
      </p>
      <Link to={ROUTES.LOGIN}>
        <Button className='uppercase mt-16' variant='borderless'>
          {translate('button_go_to_login')}
        </Button>
      </Link>
    </div>
  )
}
