import { type ActionFunctionArgs } from 'react-router'
import { makeRegisterService } from '~/application/usecases/auth'
import type { RegisterActionType } from './types/register-action-data-type'
import { formatFormDataToObject } from '~/presentation/utils/format-formdata-to-object'
import type { TenantRegisterModel } from '~/domain/models/tenant-register-model'

export async function registerAction({
  request
}: ActionFunctionArgs): Promise<RegisterActionType> {
  const tenantRegisterService = makeRegisterService()
  const formData = await request.formData()

  const registerParams = formatFormDataToObject<TenantRegisterModel>(formData)

  delete registerParams?.admin?.passwordConfirmation

  if (!registerParams?.admin?.fullName) return { error: 'Fullname is required' }

  const [firstName, ...lastName] = registerParams?.admin?.fullName?.split(' ')

  const formattedSlug = registerParams?.tenant?.slug?.replace('http://', '')

  const responseOrError = await tenantRegisterService.execute({
    admin: {
      email: registerParams?.admin?.email,
      firstName: firstName,
      lastName: lastName?.join(' '),
      language: registerParams?.admin?.language,
      password: registerParams?.admin?.password,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    },
    tenant: {
      name: registerParams?.tenant?.name,
      slug: formattedSlug
    }
  })

  if (responseOrError.isFailure()) {
    return { error: responseOrError.error.error.errors }
  }

  return {
    isSuccess: true
  }
}
