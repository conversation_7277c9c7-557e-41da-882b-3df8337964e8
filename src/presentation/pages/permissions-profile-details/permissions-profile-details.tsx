import { useBreadcrumb } from './hooks/use-breadcrumb'
import { PageTitle } from '~/presentation/components/page-title/page-title'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { ROUTES } from '~/main/types'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bs<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
} from 'softo-design-system'
import { DataGridSwitch } from './components/data-grid-switch/data-grid-switch'
import { ListUsers } from './components/list-users/list-users'
import { useState } from 'react'
import { DrawerEditProfile } from './components/drawer-edit-profile/drawer-edit-profile'
import { useMediaQuery } from '~/presentation/hooks/use-media-query'
import { loadRoleQuery } from './query/load-role-query'
import { useParams } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import { When } from '~/presentation/components/when/when'

const PermissionsProfileDetails = () => {
  const { translate } = useTranslation('permissions-profile')
  const isMobile = useMediaQuery('(max-width:768px)')
  const [openEditProfile, setOpenEditProfile] = useState(false)
  const { breadcrumb } = useBreadcrumb()
  const { roleId } = useParams()

  const { data: role } = useQuery({
    ...loadRoleQuery({
      roleId: roleId as string
    })
  })

  const handleOpenDrawerEdit = () => setOpenEditProfile((prev) => !prev)

  const isRoleDefault = role?.isAdminDefault || role?.isUserDefault

  return (
    <section className='px-4 md:px-28 py-10 overflow-auto h-[calc(100vh-104px)]'>
      <Breadcrumbs items={breadcrumb} />

      <section className='flex flex-col mt-20 max-md:mt-10 max-md:max-w-full'>
        <div className='flex'>
          <PageTitle
            title={translate(
              isMobile ? 'details_page_title_mobile' : 'details_page_title'
            )}
            previousPage={ROUTES.PERMISSIONS_PROFILE}
          />
          <When condition={!isRoleDefault}>
            <Button className='md:hidden' onClick={handleOpenDrawerEdit}>
              {translate('edit_button')}
            </Button>
          </When>
        </div>
        <div className='flex items-center justify-between mt-8'>
          <div className='flex items-center gap-6'>
            <h2 className='text-base md:text-[22px] font-semibold md:font-bold text-text-icon-high-emphasis'>
              {role?.name}
            </h2>
            <When condition={!isRoleDefault}>
              <Button
                variant='borderless'
                className='hidden md:block'
                onClick={handleOpenDrawerEdit}
              >
                {translate('edit_button')}
              </Button>
            </When>
          </div>

          <When condition={isRoleDefault}>
            <Badge className='uppercase min-w-max px-2' variant='outline'>
              {role?.isAdminDefault ? 'admin default' : 'user default'}
            </Badge>
          </When>
        </div>
      </section>

      <Tabs defaultValue='features' className='w-full mt-7'>
        <TabsList className='border-b border-border-stroke-default w-full flex justify-start'>
          <TabsTrigger value='features'>
            {translate('label_functionalities')}
          </TabsTrigger>
          <TabsTrigger value='list-users'>
            {translate('label_user_list')}
          </TabsTrigger>
        </TabsList>
        <TabsContent value='features' className='flex flex-col'>
          <DataGridSwitch />
        </TabsContent>
        <TabsContent value='list-users'>
          <ListUsers />
        </TabsContent>
      </Tabs>

      <DrawerEditProfile
        open={openEditProfile}
        onClose={handleOpenDrawerEdit}
        roleName={role?.name as string}
      />
    </section>
  )
}

export default PermissionsProfileDetails
