import type { BreadcrumbItemProps } from 'softo-design-system'
import { NavLink } from 'react-router'
import { ROUTES } from '~/main/types'
import { useTranslation } from '~/presentation/hooks/use-translation'

export const useBreadcrumb = () => {
  const { translate } = useTranslation('permissions-profile')

  const breadcrumb: BreadcrumbItemProps[] = [
    {
      link: <NavLink to={ROUTES.HOME}>{translate('breadcrumb_home')}</NavLink>,
      icon: 'home'
    },
    {
      link: (
        <NavLink to={ROUTES.PERMISSIONS_PROFILE}>
          {translate('breadcrumb_profile_permissions')}
        </NavLink>
      )
    },
    {
      link: (
        <NavLink to={ROUTES.PERMISSIONS_PROFILE}>
          {translate('breadcrumb_details')}
        </NavLink>
      )
    }
  ]

  return { breadcrumb }
}
