import { type ActionFunctionArgs } from 'react-router'
import type { QueryClient } from '@tanstack/react-query'

import { loadRoleQuery } from './query/load-role-query'

export const permissionsProfileDetailsLoader =
  (queryClient: QueryClient) =>
  async ({ params }: ActionFunctionArgs) => {
    await queryClient.ensureQueryData(
      loadRoleQuery({ roleId: params?.roleId as string })
    )

    return null
  }
