import type { PermissionsGroup } from '~/domain/models/roles/load-roles-permissions-model'

export const transformPermissions = (
  data: PermissionsGroup
): PermissionsGroup => {
  return data.map((item: any) => {
    const newItem = {
      ...item,
      open: false, // Inicializa o estado aberto
      checked: false // Inicializa checked como false
    }

    if (item.children && item.children.length > 0) {
      newItem.children = transformPermissions(item.children as any) as any // Transforma recursivamente os filhos

      // Verifica se todos os filhos do último nível estão com checked
      const allChildrenChecked = newItem.children.every((child: any) => {
        if (child.children) {
          // Se o filho tem filhos, verifica se todos os filhos estão checked
          return child.checked // Retorna o estado checked do grupo
        }
        // Se for uma permissão, verifica isActive
        return child.isActive // Retorna true se isActive for true
      })

      // Atualiza o checked do grupo pai baseado nos filhos
      newItem.checked = allChildrenChecked
    } else {
      // Se não houver filhos, verifica isActive para definir checked
      newItem.checked = item.isActive || false // Define checked baseado em isActive
    }

    return newItem
  })
}
