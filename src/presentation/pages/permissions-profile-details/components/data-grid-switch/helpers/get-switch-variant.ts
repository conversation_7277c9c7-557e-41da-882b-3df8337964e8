import type {
  Children,
  Group,
  Permission
} from '~/domain/models/roles/load-roles-permissions-model'

export const getSwitchVariant = (group: Group | Children) => {
  // Check if any child group has checked permissions
  const hasCheckedChildGroup = group?.children?.some(
    (child: any) =>
      child.type === 'group' &&
      child?.children?.some((permission: Permission) => permission.checked)
  )

  // Check if any permission in the current group is checked
  const hasCheckedPermission = group?.children?.some(
    (child) => child.type === 'permission' && child.checked
  )

  // If any child group or permission is checked, return 'primary-group'
  if (hasCheckedChildGroup || hasCheckedPermission) {
    return 'primary-group'
  }

  // If no children are checked, return 'primary'
  return 'primary'
}
