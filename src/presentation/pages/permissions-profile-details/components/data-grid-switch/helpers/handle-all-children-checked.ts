import type {
  Children,
  Permission
} from '~/domain/models/roles/load-roles-permissions-model'

export const handleAllChildrenChecked = (
  children: (Children | Permission)[]
): boolean => {
  return children.every((child) => {
    if ('children' in child) {
      // If the child has a 'children' property, it's of type Children
      return (
        child.checked ||
        (child.children && handleAllChildrenChecked(child.children))
      )
    }
    // If the child is of type Permission, just check its 'checked' property
    return child.checked
  })
}
