import { cn } from '~/presentation/utils/cn'

type Props = {
  enabledMarkNamePermission: boolean
  permissionSearch: string
  permissionName: string
}

export const MarkNamePermission = ({
  enabledMarkNamePermission,
  permissionSearch,
  permissionName
}: Props) => {
  return (
    <span
      className={cn('text-sm md:text-base', {
        'bg-palettes-primary-10 text-white px-2':
          enabledMarkNamePermission &&
          permissionName?.toLowerCase().includes(permissionSearch.toLowerCase())
      })}
    >
      {permissionName}
    </span>
  )
}
