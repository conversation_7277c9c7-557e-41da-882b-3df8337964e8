import {
  Icons,
  Switch,
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent
} from 'softo-design-system'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { cn } from '~/presentation/utils/cn'
import {
  loadRolesPermissionsQuery,
  queryKeyLoadRolesPermissions
} from '../../query/load-roles-permissions-query'
import { useParams } from 'react-router'
import { transformPermissions } from './helpers/transform-permissions'
import { getSwitchVariant } from './helpers/get-switch-variant'
import { handleAllChildrenChecked } from './helpers/handle-all-children-checked'
import { handleCheckAllChildren } from './helpers/handle-check-all-children'
import {
  makeUpdateRolesPermissionsService,
  type UpdatePermission
} from '~/application/usecases/roles/update-permission'
import { queryClient } from '~/main/config/query/queryClient'
import type { AvailableLanguages } from '~/presentation/types'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { Search } from 'softo-design-system'
import { useDebounce } from '~/presentation/hooks/use-debounce'
import { MarkNamePermission } from './components/mark-name-permission/mark-name-permission'
import { NoData } from '~/presentation/components/no-data/no-data'
import { When } from '~/presentation/components/when/when'

export const DataGridSwitch = () => {
  const { translate, language } = useTranslation('permissions-profile')
  const { roleId } = useParams<{ roleId: string }>()
  const [userSearch, setUserSearch] = useState('')
  const [openCollapses, setOpenCollapses] = useState<{
    [key: string]: boolean
  }>({})

  const permissionDebouncedValue = useDebounce(userSearch, 1000)

  const updatePermissionService = makeUpdateRolesPermissionsService()

  const {
    data: rolesPermissions,
    refetch,
    isSuccess
  } = useQuery({
    ...loadRolesPermissionsQuery({
      roleId: roleId as string,
      language: language as AvailableLanguages,
      keywords: permissionDebouncedValue
    }),
    enabled: !!permissionDebouncedValue
  })

  useEffect(() => {
    if (language) {
      refetch()
    }
  }, [language])

  const enabledMarkNamePermission =
    permissionDebouncedValue.length >= 2 && isSuccess

  const [groupPermissionsProfile, setGroupPermissionsProfile] =
    useState(rolesPermissions)

  const { mutate: updatePermission } = useMutation({
    mutationFn: (params: UpdatePermission.Params) =>
      updatePermissionService.execute(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [queryKeyLoadRolesPermissions]
      })
    }
  })

  useEffect(() => {
    if (rolesPermissions) {
      const transformedData = transformPermissions(rolesPermissions)
      setGroupPermissionsProfile(transformedData)
    }
  }, [rolesPermissions])

  const handleOpenCollapse = (groupIndex: number, childIndex?: number) => {
    setOpenCollapses((prev) => {
      const newState = { ...prev }
      if (childIndex !== undefined) {
        // For the second level (child groups)
        const key = `${groupIndex}-${childIndex}`
        newState[key] = !newState[key] // Toggles the collapse state
      } else {
        // For the first level (main groups)
        newState[groupIndex] = !newState[groupIndex] // Toggles the collapse state
      }
      return newState
    })
  }

  const handleSwitchChange = (
    groupIndex: number,
    childIndex?: number,
    permissionIndex?: number
  ) => {
    setGroupPermissionsProfile((prevState) => {
      const newState = [...(prevState || [])]
      if (permissionIndex !== undefined && childIndex !== undefined) {
        const permission =
          newState[groupIndex]?.children?.[childIndex]?.children?.[
            permissionIndex
          ]
        if (permission) {
          permission.checked = !permission.checked

          // Check if all children are checked
          const allChecked = handleAllChildrenChecked(
            newState[groupIndex].children[childIndex].children
          )
          newState[groupIndex].children[childIndex].checked = allChecked

          // Check if the parent group should be checked
          const parentAllChecked = handleAllChildrenChecked(
            newState[groupIndex].children
          )
          newState[groupIndex].checked = parentAllChecked
        }
      } else if (childIndex !== undefined) {
        // If a child group is being checked/unchecked
        const childGroup = newState[groupIndex].children[childIndex]
        childGroup.checked = !childGroup.checked

        // Check or uncheck all permissions in this child group
        handleCheckAllChildren(childGroup.children, childGroup.checked)

        // Check if the parent group should be checked
        const parentAllChecked = handleAllChildrenChecked(
          newState[groupIndex].children
        )
        newState[groupIndex].checked = parentAllChecked
      } else {
        // If the parent is being checked/unchecked
        const isChecked = !newState[groupIndex].checked
        newState[groupIndex].checked = isChecked

        // Check or uncheck all children
        handleCheckAllChildren(newState[groupIndex].children, isChecked)
      }
      return newState
    })
  }

  return (
    <>
      <div className='max-w-[340px] mt-4'>
        <Search
          name='search'
          placeholder={translate('placeholder_search_functionality')}
          className='w-full'
          value={userSearch}
          onChange={(e) => setUserSearch(e.target.value)}
          maxLength={100}
          onClear={() => setUserSearch('')}
        />
      </div>

      <section className='mt-6'>
        <When condition={!groupPermissionsProfile?.length}>
          <NoData classNameRow='border-0' isTable={false} />
        </When>
        {groupPermissionsProfile?.map((group, groupIndex) => (
          <Collapsible
            key={group.id}
            open={openCollapses[groupIndex] || false}
            onOpenChange={() => handleOpenCollapse(groupIndex)}
          >
            <div className='flex items-center gap-5 pl-3 py-4 h-16 pr-2 md:pr-0 bg-white'>
              <CollapsibleTrigger>
                <Icons
                  icon='arrow_drop_down'
                  className={cn(' text-palettes-neutral-50 transition-all', {
                    '-rotate-90': !openCollapses[groupIndex]
                  })}
                />
              </CollapsibleTrigger>
              <Switch
                checked={group.checked}
                variant={getSwitchVariant(group)}
                onCheckedChange={() => {
                  handleSwitchChange(groupIndex)
                  updatePermission({
                    active: group.checked as boolean,
                    id: group.id,
                    type: group.type,
                    roleId: roleId as string
                  })
                }}
              />
              <span className='h-4 w-[2px] rounded-xl bg-palettes-primary-10 block' />
              <MarkNamePermission
                enabledMarkNamePermission={enabledMarkNamePermission}
                permissionName={group.name}
                permissionSearch={permissionDebouncedValue}
              />
            </div>
            <CollapsibleContent>
              {group.children.map((groupItem, groupItemIdex) => (
                <Collapsible
                  key={groupItem.name}
                  open={
                    openCollapses[`${groupIndex}-${groupItemIdex}`] || false
                  }
                  onOpenChange={() =>
                    handleOpenCollapse(groupIndex, groupItemIdex)
                  }
                >
                  <div>
                    <div
                      className={`flex items-center gap-5 py-4 h-16 relative pl-6 pr-2 md:pr-0 ${groupItemIdex % 2 === 0 ? 'bg-background-table-row' : 'bg-white'}`}
                    >
                      <span className='w-px rounded-xl bg-palettes-neutral-70 block h-full absolute' />
                      <CollapsibleTrigger className='ml-6'>
                        <Icons
                          icon='arrow_drop_down'
                          className={cn(
                            ' text-palettes-neutral-50 transition-all',
                            {
                              '-rotate-90':
                                !openCollapses[`${groupIndex}-${groupItemIdex}`]
                            }
                          )}
                        />
                      </CollapsibleTrigger>
                      <Switch
                        checked={groupItem.checked}
                        variant={getSwitchVariant(groupItem)}
                        onCheckedChange={() => {
                          handleSwitchChange(groupIndex, groupItemIdex)
                          updatePermission({
                            active: groupItem.checked as boolean,
                            id: groupItem.id,
                            type: groupItem.type,
                            roleId: roleId as string
                          })
                        }}
                      />
                      <span className='h-4 w-[2px] rounded-xl bg-palettes-primary-30 block' />
                      <MarkNamePermission
                        enabledMarkNamePermission={enabledMarkNamePermission}
                        permissionName={groupItem.name}
                        permissionSearch={permissionDebouncedValue}
                      />
                    </div>
                    <CollapsibleContent>
                      {groupItem.children?.map(
                        (permission, permissionIndex) => (
                          <div
                            className={`flex items-center gap-5 py-4 h-16 relative pl-6 pr-2 md:pr-0 ${permissionIndex % 2 === 0 ? 'bg-white' : 'bg-background-table-row'}`}
                            key={permission.name}
                          >
                            <span className='w-px rounded-xl bg-palettes-neutral-70 block h-full absolute' />
                            <div className='pl-9 flex items-center gap-5'>
                              <span className='w-px rounded-xl bg-palettes-neutral-70 block h-full absolute' />
                              <Switch
                                checked={permission.checked}
                                onCheckedChange={() => {
                                  handleSwitchChange(
                                    groupIndex,
                                    groupItemIdex,
                                    permissionIndex
                                  )
                                  updatePermission({
                                    active: permission.checked as boolean,
                                    id: permission.id,
                                    type: permission.type,
                                    roleId: roleId as string
                                  })
                                }}
                                className='ml-14'
                              />
                              <span className='h-4 w-[2px] rounded-xl bg-palettes-primary-80 block' />
                              <MarkNamePermission
                                enabledMarkNamePermission={
                                  enabledMarkNamePermission
                                }
                                permissionName={permission.name}
                                permissionSearch={permissionDebouncedValue}
                              />
                            </div>
                          </div>
                        )
                      )}
                    </CollapsibleContent>
                  </div>
                </Collapsible>
              ))}
            </CollapsibleContent>
          </Collapsible>
        ))}
      </section>
    </>
  )
}
