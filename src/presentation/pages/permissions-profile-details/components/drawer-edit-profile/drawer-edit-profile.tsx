import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet,
  She<PERSON><PERSON><PERSON><PERSON>
} from 'softo-design-system'

import { ConfirmModal, TextInput } from '~/presentation/components'
import { useParams } from 'react-router'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { validationSchema } from './validations/edit-role-validation'
import { useMutation } from '@tanstack/react-query'
import {
  makeUpdateRoleService,
  type UpdateRole
} from '~/application/usecases/roles/update-role'
import { queryClient } from '~/main/config/query/queryClient'
import { queryKeyLoadRole } from '../../query/load-role-query'
import { Spinner } from '~/presentation/components/spinner/spinner'
import { useToast } from '~/presentation/hooks/use-toast'
import { ToastWrapper } from '~/presentation/components/toast-wrapper/toast-wrapper'
import { useTranslation } from 'react-i18next'
import { useState } from 'react'
import { queryKeyLoadRoles } from '~/presentation/querys/roles/load-roles-query'
import type { DomainException } from '~/domain/exceptions/domain-exception'

type Props = {
  open: boolean
  onClose: () => void
  roleName: string
  roleIdParams?: string
}

type FormModel = {
  name: string
}

export const DrawerEditProfile = ({
  open,
  onClose,
  roleName,
  roleIdParams
}: Props) => {
  const { roleId } = useParams()
  const { t: translate } = useTranslation('permissions-profile')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const updateRoleService = makeUpdateRoleService()
  const { isToastOpen, showToast, hideToast, toastDescription, toastVariant } =
    useToast()

  const onToggleModalConfirm = () => setIsModalOpen((prev) => !prev)

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isLoading: isLoadingSubmit }
  } = useForm<FormModel>({
    resolver: zodResolver(validationSchema),
    mode: 'onChange'
  })

  const { mutate: updateRole, isPending } = useMutation({
    mutationFn: (params: UpdateRole.Params) => {
      return updateRoleService.execute(params)
    },
    onSuccess: () => {
      showToast(
        `${translate('edit_profile_name_sucesss', { name: watch('name') })}`,
        'success'
      )
      queryClient.invalidateQueries({
        queryKey: [queryKeyLoadRole]
      })
      queryClient.invalidateQueries({
        queryKey: [queryKeyLoadRoles]
      })
      onClose()
    },
    onError: (error: DomainException) => {
      onClose()
      if (error?.error?.message === 'Role already exists') {
        showToast(translate('message_errors_role_exists'), 'error')
      } else {
        showToast()
      }
    }
  })

  const onSubmit = (data: FormModel) => {
    updateRole({ ...data, roleId: (roleId || roleIdParams) as string })
  }

  const isLoadingSave = isPending || isLoadingSubmit

  return (
    <>
      <ConfirmModal
        isModalOpen={isModalOpen}
        setOpenModal={setIsModalOpen}
        handleResetAndClose={onClose}
        title={translate('common:cancel_title')}
        description={translate('edit_cancel_description')}
        btnSecondaryText={translate('common:btn_return')}
        btnPrimaryText={translate('common:btn_confirm')}
      />
      <ToastWrapper
        toastVariant={toastVariant}
        description={toastDescription}
        isToastOpen={isToastOpen}
        onClose={hideToast}
      />
      <Sheet open={open}>
        <SheetContent className='bg-white max-h-full min-h-full rounded-none xs:w-[100vw] w-full sm:max-w-[720px] sm:w-[720px] md:w-[720px] p-0'>
          <SheetHeader className='w-full p-6 flex justify-end items-end border-b'>
            <Button onClick={onClose} variant='ghost' className='w-12 h-12'>
              <Icons icon='close' wrapperClassname='flex items center' />
            </Button>
          </SheetHeader>

          <div className='flex flex-col items-center w-full px-6 md:px-0 md:max-w-[720px] mx-auto h-screen overflow-y-auto overflow-x-hidden py-20 md:py-28 bg-white'>
            <form
              className='w-full h-full md:w-[440px] flex flex-col items-center bg-white'
              onSubmit={handleSubmit(onSubmit)}
            >
              <SheetTitle className='text-xl text-center md:text-[28px] mb-3 md:mb-0 uppercase text-black font-bold'>
                {translate('edit_profile_name_title')}
              </SheetTitle>
              <SheetDescription className='text-base font-normal mt-2 text-center'>
                {translate('edit_profile_name_description')}
              </SheetDescription>

              <TextInput
                {...register('name')}
                label={translate('edit_profle_field_name')}
                placeholder={translate('edit_profile_field_placeholder')}
                wrapperClassname='w-full mt-16'
                error={errors.name}
                defaultValue={roleName}
                maxLength={100}
              />

              <div className='flex gap-5 w-full justify-center items-center'>
                <Button
                  size='lg'
                  variant='ghost'
                  type='button'
                  className='w-[211px] uppercase'
                  onClick={onToggleModalConfirm}
                >
                  {translate('edit_profile_btn_cancel')}
                </Button>
                <Button type='submit' size='lg' className='w-[211px] uppercase'>
                  {isLoadingSave ? (
                    <Spinner />
                  ) : (
                    translate('edit_profile_btn_send')
                  )}
                </Button>
              </div>
            </form>
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
}
