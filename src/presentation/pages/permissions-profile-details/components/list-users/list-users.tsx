import { useState } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useToast } from '~/presentation/hooks/use-toast'
import {
  loadRoleUsersQuery,
  queryKeyLoadRoleUsers
} from '../../query/load-role-users-query'
import { DataGrid } from '~/presentation/components/data-grid/data-grid'
import type { LoadRoleUsers } from '~/application/usecases/roles/load-role-users'
import { useParams } from 'react-router'
import { DataGridRow } from './components/data-grid-row/data-grid-row'
import { Search } from 'softo-design-system'
import { type LoadRoleUsersLinked } from '~/application/usecases/roles/load-role-users-linked'
import { useDebounce } from '~/presentation/hooks/use-debounce'
import { type LoadRoleUsersUnLinked } from '~/application/usecases/roles/load-role-users-unlinked'
import { When } from '~/presentation/components/when/when'
import {
  loadRoleUsersLinkedQuery,
  queryKeyLoadRoleUsersLinked
} from './query/load-role-users-linked-query'
import {
  loadRoleUsersUnLinkedQuery,
  queryKeyLoadRoleUsersUnLinked
} from './query/load-role-users-unlinked-query'
import { makeAddRoleUserService } from '~/application/usecases/roles/add-role-user'
import { makeDeleteRoleUserService } from '~/application/usecases/roles/delete-role-user'
import { queryClient } from '~/main/config/query/queryClient'
import { NoData } from '~/presentation/components/no-data/no-data'
import { ToastWrapper } from '~/presentation/components/toast-wrapper/toast-wrapper'
import type { LoadRoleUsersModel } from '~/domain/models/roles/load-role-users-model'
import { Trans, useTranslation } from 'react-i18next'

export const ListUsers = () => {
  const { t: translate } = useTranslation('permissions-profile')
  const { roleId } = useParams<{ roleId: string }>()
  const [userSearch, setUserSearch] = useState('')
  const [loadingUserId, setLoadingUserId] = useState<string | null>(null)

  const { isToastOpen, showToast, hideToast, toastDescription, toastVariant } =
    useToast()

  const addRoleUser = makeAddRoleUserService()
  const deleteRoleUser = makeDeleteRoleUserService()

  const userDebouncedValue = useDebounce(userSearch, 1000)

  const { data: users } = useQuery({
    ...loadRoleUsersQuery({ roleId: roleId as string })
  })

  const { data: usersLinked } = useQuery({
    ...loadRoleUsersLinkedQuery({
      roleId: roleId as string,
      keywords: userDebouncedValue
    }),
    enabled: !!userDebouncedValue
  })

  const { data: usersUnLinked } = useQuery({
    ...loadRoleUsersUnLinkedQuery({
      roleId: roleId as string,
      keyworkds: userDebouncedValue
    }),
    enabled: !!userDebouncedValue
  })

  const { mutate: addUserRole } = useMutation({
    mutationFn: (user: LoadRoleUsersModel) =>
      addRoleUser
        .execute({ roleId: roleId as string, id: user.id })
        .finally(() => setLoadingUserId(null)),
    onSuccess: (_, variables: LoadRoleUsersModel) => {
      showToast(
        <Trans
          i18nKey='permissions-profile:add_user_role_success'
          values={{ name: variables?.name }}
          components={{ b: <b /> }}
        />,
        'success'
      )
      const queryKeysToInvalidate = [
        queryKeyLoadRoleUsersUnLinked,
        queryKeyLoadRoleUsers,
        queryKeyLoadRoleUsersLinked
      ]

      queryKeysToInvalidate.forEach((queryKey) => {
        queryClient.invalidateQueries({ queryKey: [queryKey] })
      })
    }
  })

  const { mutate: deleteUserRole } = useMutation({
    mutationFn: (user: LoadRoleUsersModel) =>
      deleteRoleUser
        .execute({ roleId: roleId as string, userId: user.id })
        .finally(() => setLoadingUserId(null)),
    onSuccess: (_, variables: LoadRoleUsersModel) => {
      showToast(
        <Trans
          i18nKey='permissions-profile:remove_user_role_success'
          values={{ name: variables?.name }}
          components={{ b: <b /> }}
        />,
        'success'
      )
      const queryKeysToInvalidate = [
        queryKeyLoadRoleUsersUnLinked,
        queryKeyLoadRoleUsers,
        queryKeyLoadRoleUsersLinked
      ]

      queryKeysToInvalidate.forEach((queryKey) => {
        queryClient.invalidateQueries({ queryKey: [queryKey] })
      })
    }
  })

  return (
    <>
      <ToastWrapper
        toastVariant={toastVariant}
        description={toastDescription}
        isToastOpen={isToastOpen}
        onClose={hideToast}
      />
      <section className='mt-4'>
        <div className='max-w-[340px]'>
          <Search
            name='search'
            placeholder={translate('placeholder_search_users')}
            className='w-full'
            value={userSearch}
            onChange={(e) => setUserSearch(e.target.value)}
            onClear={() => setUserSearch('')}
          />
        </div>

        <div className='mt-6'>
          <h2 className='text-action-secondary-default uppercase text-sm font-bold'>
            {translate('users_associated_title')}
          </h2>

          <When condition={!usersLinked}>
            <DataGrid<LoadRoleUsers.Response>
              className='mt-5'
              disabledPagination={users?.rows?.length === 0}
              loadQuery={(params) =>
                loadRoleUsersQuery({
                  ...params,
                  roleId: roleId as string
                })
              }
            >
              {users?.rows?.map((user, index) => (
                <DataGridRow
                  onAction={() => {
                    setLoadingUserId(user.id)
                    deleteUserRole(user)
                  }}
                  key={user.id}
                  user={user}
                  index={index}
                  isLoading={loadingUserId === user.id}
                />
              ))}
              <When condition={!users?.rows?.length}>
                <NoData />
              </When>
            </DataGrid>
          </When>

          <When condition={!!userDebouncedValue}>
            <DataGrid<LoadRoleUsersLinked.Response>
              className='mt-5'
              disabledPagination
            >
              {usersLinked?.map((user, index) => (
                <DataGridRow
                  onAction={() => {
                    setLoadingUserId(user.id)
                    deleteUserRole(user)
                  }}
                  key={user.id}
                  user={user}
                  index={index}
                  isLoading={loadingUserId === user.id}
                />
              ))}
              <When condition={!usersLinked?.length}>
                <NoData />
              </When>
            </DataGrid>
          </When>

          <When condition={!!userDebouncedValue}>
            <div className='mt-6 pt-5 border-t border-border-stroke-default'>
              <h2 className='text-action-secondary-default uppercase text-sm font-bold'>
                {translate('users_unassociated_title')}
              </h2>
            </div>

            <DataGrid<LoadRoleUsersUnLinked.Response>
              className='mt-6'
              disabledPagination
            >
              {usersUnLinked?.map((user, index) => (
                <DataGridRow
                  isUnLinked
                  onAction={() => {
                    setLoadingUserId(user.id)
                    addUserRole(user)
                  }}
                  key={user.id}
                  user={user}
                  index={index}
                  isLoading={loadingUserId === user.id}
                />
              ))}
              <When condition={!usersUnLinked?.length}>
                <NoData />
              </When>
            </DataGrid>
          </When>
        </div>
      </section>
    </>
  )
}
