import { makeLoadRoleUsersUnLinkedService } from '~/application/usecases/roles/load-role-users-unlinked'
import { createQueryFn } from '~/presentation/querys/helpers/create-query'

export const queryKeyLoadRoleUsersUnLinked = 'loadRoleUsersUnLinked'

type Props = {
  roleId: string
  keyworkds: string
}

export const loadRoleUsersUnLinkedQuery = (params: Props) => {
  const loadRoleUsersUnLinkedService = makeLoadRoleUsersUnLinkedService()

  return {
    queryKey: [queryKeyLoadRoleUsersUnLinked, params.roleId, params.keyworkds],
    queryFn: createQueryFn(loadRoleUsersUnLinkedService, {
      roleId: params.roleId,
      keywords: params.keyworkds
    })
  }
}
