import { makeLoadRoleUsersLinkedService } from '~/application/usecases/roles/load-role-users-linked'
import { createQueryFn } from '~/presentation/querys/helpers/create-query'

export const queryKeyLoadRoleUsersLinked = 'loadRoleUsersLinked'

type Props = {
  roleId: string
  keywords: string
}

export const loadRoleUsersLinkedQuery = (params: Props) => {
  const loadRoleUsersLinkedService = makeLoadRoleUsersLinkedService()

  return {
    queryKey: [queryKeyLoadRoleUsersLinked, params.roleId, params.keywords],
    queryFn: createQueryFn(loadRoleUsersLinkedService, {
      roleId: params.roleId,
      keywords: params.keywords
    })
  }
}
