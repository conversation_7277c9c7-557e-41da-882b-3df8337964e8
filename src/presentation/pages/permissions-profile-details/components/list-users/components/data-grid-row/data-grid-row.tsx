import { Badge, <PERSON><PERSON>, Icons, TableCell, TableRow } from 'softo-design-system'
import type { LoadRoleUsersModel } from '~/domain/models/roles/load-role-users-model'
import { Spinner } from '~/presentation/components/spinner/spinner'
import { When } from '~/presentation/components/when/when'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { useMediaQuery } from '~/presentation/hooks/use-media-query'

type Props = {
  user: LoadRoleUsersModel
  index: number
  onAction: () => void
  isUnLinked?: boolean
  isLoading: boolean
}

export const DataGridRow = ({
  user,
  index,
  onAction,
  isUnLinked,
  isLoading
}: Props) => {
  const { translate } = useTranslation('permissions-profile')
  const isMobile = useMediaQuery('(max-width:768px)')

  return (
    <TableRow
      className={`h-16 border-0 grid grid-cols-[50%_40%_10%] lg:grid-cols-[70%_25%_5%] items-center md:w-full ${index % 2 === 0 ? 'bg-white' : 'bg-background-table-row'}`}
    >
      <TableCell className='p-0 md:pl-4 gap-4 flex md:gap-0 cursor-pointer min-w-[200px]'>
        <div className='flex items-center gap-4 w-full'>
          <div className='h-8 w-8 md:h-10 md:w-10 rounded-full bg-palettes-neutral-95 flex items-center justify-center'>
            <Icons
              icon='person'
              variant='round'
              size={isMobile ? 20 : 28}
              className='text-text-icon-medium-emphasis'
              wrapperClassname='flex items-center'
            />
          </div>
          <div className='flex flex-col'>
            <span className='text-sm text-text-icon-high-emphasis'>
              {user.name}
            </span>
            <span className='text-xs text-text-icon-medium-emphasis'>
              {user.email}
            </span>
          </div>
        </div>
      </TableCell>

      <TableCell className='p-0 md:p-2'>
        <Badge
          className='uppercase min-w-max px-2 text-[10px] md:text-xs'
          variant={user.status ? 'inverted-success' : 'inverted-error'}
        >
          {user.status === 'USER_ACTIVE'
            ? translate('active_user')
            : translate('inactive_user')}
        </Badge>
      </TableCell>

      <TableCell className='p-0 md:p-2'>
        <Button
          variant='borderless'
          className='w-6 h-6 p-4'
          type='button'
          onClick={onAction}
        >
          <When condition={isLoading}>
            <Spinner
              color='border-t-border-stroke-brand-default'
              width='w-5'
              height='h-5'
            />
          </When>
          <When condition={!isLoading}>
            <Icons
              icon={isUnLinked ? 'add' : 'delete'}
              variant='outlined'
              className='text-palettes-neutral-50'
              wrapperClassname='flex items-center'
            />
          </When>
        </Button>
      </TableCell>
    </TableRow>
  )
}
