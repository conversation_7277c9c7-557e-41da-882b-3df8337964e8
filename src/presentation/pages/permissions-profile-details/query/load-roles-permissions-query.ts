import { makeLoadRolesPermissionsService } from '~/application/usecases/roles/load-roles-permissions'
import { createQueryFn } from '~/presentation/querys/helpers/create-query'
import type { AvailableLanguages } from '~/presentation/types'

type Props = {
  roleId: string
  language: AvailableLanguages
  keywords?: string
}

export const queryKeyLoadRolesPermissions = 'loadRolesPermissions'

export const loadRolesPermissionsQuery = (params: Props) => {
  const loadRolesPermissionsService = makeLoadRolesPermissionsService()

  return {
    queryKey: [queryKeyLoadRolesPermissions, params.roleId, params?.keywords],
    queryFn: createQueryFn(loadRolesPermissionsService, {
      roleId: params.roleId,
      language: params.language,
      keywords: params?.keywords
    })
  }
}
