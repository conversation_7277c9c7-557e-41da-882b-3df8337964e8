import { makeLoadRoleService } from '~/application/usecases/roles/load-role'
import { createQueryFn } from '~/presentation/querys/helpers/create-query'

type Props = {
  roleId: string
}

export const queryKeyLoadRole = 'loadRole'

export const loadRoleQuery = (params: Props) => {
  const loadRoleService = makeLoadRoleService()

  return {
    queryKey: [queryKeyLoadRole, params.roleId],
    queryFn: createQueryFn(loadRoleService, {
      roleId: params.roleId
    })
  }
}
