import { makeLoadRoleUsersService } from '~/application/usecases/roles/load-role-users'
import { createQueryFn } from '~/presentation/querys/helpers/create-query'

export const queryKeyLoadRoleUsers = 'loadRoleUsers'

type Props = {
  roleId: string
  pageSize?: number
  page?: number
}

export const loadRoleUsersQuery = (params: Props) => {
  const loadRoleUsersService = makeLoadRoleUsersService()

  return {
    queryKey: [queryKeyLoadRoleUsers, params.roleId],
    queryFn: createQueryFn(loadRoleUsersService, {
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      roleId: params.roleId
    })
  }
}
