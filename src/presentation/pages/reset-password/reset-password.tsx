import { useForm } from 'react-hook-form'
import { useFetcher } from 'react-router'
import { isEmpty } from 'lodash'
import { zodResolver } from '@hookform/resolvers/zod'

import { ROUTES } from '~/main/types'
import { Link, TextInput } from '~/presentation/components'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { Spinner } from '~/presentation/components/spinner/spinner'
import { SystemVersion } from '~/presentation/components/system-version/system-version'
import type { ResetPasswordType } from './types/reset-password-type'
import { validationSchema } from './validations/reset-password-validation'
import { PasswordRules } from '~/presentation/components/password-rules/password-rules'
import { validatePassword } from '~/presentation/utils/validate-password'
import { When } from '~/presentation/components/when/when'
import { SuccessResetPassword } from './components/success-reset-password/success-reset-password'

import { Button } from 'softo-design-system'

const ResetPassword = () => {
  const { translate } = useTranslation('reset-password')

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm<ResetPasswordType>({
    resolver: zodResolver(validationSchema),
    mode: 'onBlur'
  })

  const password = watch('password')
  const fetcher = useFetcher()
  const hasErrors = !isEmpty(errors)

  const { isValid: isValidPassword } = validatePassword({ password })

  const isLoadingSubmitForm =
    fetcher.state === 'loading' || fetcher.state === 'submitting'

  const onSubmit = (data: ResetPasswordType) => {
    if (!isValidPassword) return
    fetcher.submit(data, { method: 'post' })
  }

  return (
    <div className='pb-6'>
      <When condition={fetcher?.data?.isSuccess}>
        <SuccessResetPassword />
      </When>
      <When condition={!fetcher?.data?.isSuccess}>
        <fetcher.Form
          className='max-w-96 w-full mx-auto pb-0 md:pb-10'
          onSubmit={handleSubmit(onSubmit)}
        >
          <h1 className='w-full text-center uppercase text-xl font-bold mb-4'>
            {translate('title_reset_password')}
          </h1>
          <p className='mb-16 text-center'>
            {translate('subtitle_reset_password')}
          </p>

          <TextInput
            color='secondary'
            label={translate('label_password')}
            placeholder={translate('placeholder_password')}
            maxLength={20}
            error={errors?.password}
            id='password'
            isPassword
            {...register('password')}
          />

          <PasswordRules password={password} />

          <TextInput
            color='secondary'
            label={translate('label_confirm_password')}
            placeholder={translate('placeholder_confirm_password')}
            error={errors?.passwordConfirmation}
            maxLength={20}
            id='password'
            wrapperClassname={hasErrors ? 'mb-3' : 'mb-6'}
            autoComplete='current-password'
            isPassword
            {...register('passwordConfirmation')}
          />

          <div className='flex flex-col w-full gap-5'>
            <Button
              type='submit'
              disabled={isLoadingSubmitForm}
              className='w-full uppercase'
            >
              {isLoadingSubmitForm ? (
                <Spinner />
              ) : (
                translate('btn_send_reset_password')
              )}
            </Button>

            <Link to={ROUTES.LOGIN}>
              <Button
                type='button'
                className='w-full uppercase'
                variant='borderless'
              >
                {translate('btn_back_to_login')}
              </Button>
            </Link>
          </div>
        </fetcher.Form>
      </When>
      <SystemVersion />
    </div>
  )
}

export default ResetPassword
