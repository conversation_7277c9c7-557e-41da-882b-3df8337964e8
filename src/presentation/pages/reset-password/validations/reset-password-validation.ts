import { z } from 'zod'

export const validationSchema = z
  .object({
    password: z.string().min(1, 'validation_password_required'),
    passwordConfirmation: z.string().min(1, 'validation_password_different')
  })
  .superRefine(({ password, passwordConfirmation }, ctx) => {
    if (password !== passwordConfirmation) {
      ctx.addIssue({
        code: 'custom',
        message: 'validation_password_confirm',
        path: ['passwordConfirmation']
      })
    }
  })
