import { useRouteError, isRouteErrorResponse, Link } from 'react-router'
import { QueryException } from '~/domain/exceptions/query-exception'
import { ExpiredToken } from './components/expired-token/expired-token'
import { Button } from 'softo-design-system'
import { ROUTES } from '~/main/types'
import { useTranslation } from '~/presentation/hooks/use-translation'

export default function ResetPasswordError() {
  const { translate } = useTranslation('common')
  const error = useRouteError()

  let errorMessage = null
  let errorResponse

  if (isRouteErrorResponse(error)) {
    errorMessage = error.statusText
  } else if (error instanceof QueryException) {
    const customError = error
    errorResponse = customError.response
  }

  if (errorResponse?.error?.errors === 'Token is expired')
    return <ExpiredToken />

  return (
    <div className='flex flex-col items-center justify-center' id='error-page'>
      <h1 className='text-center text-[28px] font-bold text-text-icon-high-emphasis'>
        {translate('label_ops')}
      </h1>
      <p className='text-sm mt-4 text-center max-w-96'>
        {errorMessage ??
          errorResponse?.error?.errors ??
          translate('error_message')}
      </p>
      <Link to={ROUTES.LOGIN}>
        <Button className='uppercase mt-16' variant='borderless'>
          {translate('btn_go_to_login')}
        </Button>
      </Link>
    </div>
  )
}
