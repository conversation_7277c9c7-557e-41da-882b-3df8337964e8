import { type ActionFunctionArgs } from 'react-router'
import { makeResetPasswordService } from '~/application/usecases/auth/reset-password'

export async function resetPasswordAction({ request }: ActionFunctionArgs) {
  const resetPasswordService = makeResetPasswordService()
  const { password } = Object.fromEntries(await request.formData())

  const url = new URL(request.url)
  const securityToken = url.searchParams.get('securityToken')

  if (!securityToken || !password)
    return { error: 'SecurityToken or password was not found' }

  const responseOrError = await resetPasswordService.execute({
    securityToken,
    password: password as string
  })

  if (responseOrError.isFailure()) {
    return responseOrError.error
  }

  return { isSuccess: true }
}
