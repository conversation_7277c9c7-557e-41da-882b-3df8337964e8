import { screen, render, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { RouterProvider, createMemoryRouter } from 'react-router'
import { Suspense } from 'react'

import ResetPassword from './reset-password'

// Mock das deendências
vi.mock('react-hook-form', () => ({
  useForm: () => ({
    register: vi.fn(),
    handleSubmit: (fn: any) => (e: any) => {
      e.preventDefault()
      fn({ password: 'qwe123QWE@', passwordConfirmation: 'qwe123QWE@' })
    },
    formState: { isValid: true, errors: {} },
    watch: vi.fn()
  })
}))

vi.mock('react-router', async () => {
  const original = await vi.importActual('react-router')
  return {
    ...original,
    useFetcher: () => ({
      state: 'idle',
      submit: vi.fn(),
      data: { error: null },
      Form: (props: any) => <form {...props} />
    }),
    Link: original.Link
  }
})

vi.mock('./validations/reset-password-validation', () => ({
  validationSchema: {
    parse: (data: any) => data // Simula a validação bem-sucedida
  }
}))

vi.mock('~/presentation/hooks', () => ({
  useTranslation: () => ({
    translate: (key: any) => key
  })
}))

vi.mock('softo-design-system', async () => {
  const actual = await vi.importActual('softo-design-system')
  return {
    ...actual,
    Button: (props: any) => <button {...props} />
  }
})

vi.mock('~/presentation/components/spinner/spinner', () => ({
  Spinner: () => <div>Loading...</div>
}))

vi.mock('~/presentation/components/system-version/system-version', () => ({
  SystemVersion: () => <div>System Version</div>
}))

const routes = [
  {
    path: '/reset-password',
    element: (
      <Suspense>
        <ResetPassword />
      </Suspense>
    )
  },
  {
    path: '/login',
    element: <div>Login Page</div>
  }
]

const makeSut = async () => {
  const router = createMemoryRouter(routes, {
    initialEntries: ['/reset-password']
  })

  render(<RouterProvider router={router} />)

  return {
    titleResetPassword: await screen.findByText('title_reset_password'),
    subTitleElement: await screen.findByText('subtitle_reset_password'),
    passwordInput: await screen.findByPlaceholderText('placeholder_password'),
    confirmPasswordInput: await screen.findByPlaceholderText(
      'placeholder_confirm_password'
    ),
    resetPasswordButton: await screen.findByText('btn_send_reset_password'),
    backLoginButton: await screen.findByText('btn_back_to_login')
  }
}

describe('ResetPassword', () => {
  it('should display title screen and fields', async () => {
    const {
      titleResetPassword,
      subTitleElement,
      passwordInput,
      confirmPasswordInput,
      resetPasswordButton,
      backLoginButton
    } = await makeSut()

    expect(titleResetPassword).toBeInTheDocument()
    expect(subTitleElement).toBeInTheDocument()
    expect(passwordInput).toBeInTheDocument()
    expect(confirmPasswordInput).toBeInTheDocument()
    expect(resetPasswordButton).toBeInTheDocument()
    expect(backLoginButton).toBeInTheDocument()
  })

  it('should display password rules', async () => {
    makeSut()

    const passwordRulesTitle = await screen.findByText('password_rules_title')
    const firstRuleTitle = await screen.findByText(
      'validations:password_rules_01'
    )
    const secondRuleTitle = await screen.findByText(
      'validations:password_rules_02'
    )
    const thirdRuleTitle = await screen.findByText(
      'validations:password_rules_03'
    )
    const fourthRuleTitle = await screen.findByText(
      'validations:password_rules_04'
    )
    const fifthRuleTitle = await screen.findByText(
      'validations:password_rules_05'
    )

    expect(passwordRulesTitle).toBeInTheDocument()
    expect(firstRuleTitle).toBeInTheDocument()
    expect(secondRuleTitle).toBeInTheDocument()
    expect(thirdRuleTitle).toBeInTheDocument()
    expect(fourthRuleTitle).toBeInTheDocument()
    expect(fifthRuleTitle).toBeInTheDocument()
  })

  it('should navigate to login page on button click', async () => {
    const { backLoginButton } = await makeSut()

    await userEvent.click(backLoginButton)

    await waitFor(() => {
      expect(screen.getByText('Login Page')).toBeInTheDocument()
    })
  })

  it('should have enabled button on screen', async () => {
    const { resetPasswordButton } = await makeSut()
    expect(resetPasswordButton).toBeEnabled()
  })

  it('Should show max length input values', async () => {
    const { passwordInput, confirmPasswordInput, resetPasswordButton } =
      await makeSut()

    const maxLengthPassword = 'a'.repeat(21)
    const maxLengthConfirmPassword = 'c'.repeat(21)

    await userEvent.type(passwordInput, maxLengthPassword)
    await userEvent.type(confirmPasswordInput, maxLengthConfirmPassword)
    await userEvent.tab()

    waitFor(() => {
      expect(passwordInput).toHaveValue(maxLengthPassword.substring(0, 20))
      expect(confirmPasswordInput).toHaveValue(
        maxLengthConfirmPassword.substring(0, 20)
      )
      expect(resetPasswordButton).toBeEnabled()
    })
  })

  it('Should display required field error when fields is empty', async () => {
    const { passwordInput, confirmPasswordInput } = await makeSut()

    const errorPassowordInput = screen.findByText(
      'Password is a required field'
    )

    await userEvent.click(passwordInput)
    expect(passwordInput).toHaveFocus()

    waitFor(() => {
      expect(errorPassowordInput).toBeInTheDocument()
    })

    await userEvent.click(confirmPasswordInput)
    expect(confirmPasswordInput).toHaveFocus()

    waitFor(() => {
      expect(screen.findByText('Passwords are different.')).toBeInTheDocument()
    })
  })
})
