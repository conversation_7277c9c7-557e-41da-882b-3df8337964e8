import { But<PERSON> } from 'softo-design-system'
import { <PERSON> } from 'react-router'
import { ROUTES } from '~/main/types'
import { useTranslation } from '~/presentation/hooks/use-translation'

export const SuccessResetPassword = () => {
  const { translate } = useTranslation('reset-password')

  return (
    <div className='flex flex-col items-center justify-center'>
      <h1 className='text-center text-2xl md:text-[1.75rem] font-bold text-text-icon-high-emphasis'>
        {translate('title_success_reset')}
      </h1>
      <p className='text-sm mt-4 max-w-96 text-center'>
        {translate('description_success_reset')}
      </p>
      <Link to={ROUTES.LOGIN}>
        <Button className='uppercase mt-16' variant='borderless'>
          {translate('btn_back_to_login')}
        </Button>
      </Link>
    </div>
  )
}
