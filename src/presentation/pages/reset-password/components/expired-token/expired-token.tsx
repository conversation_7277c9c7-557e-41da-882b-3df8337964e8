import { But<PERSON> } from 'softo-design-system'
import { <PERSON> } from 'react-router'
import { ROUTES } from '~/main/types'
import { useTranslation } from '~/presentation/hooks/use-translation'

export const ExpiredToken = () => {
  const { translate } = useTranslation('confirm-email')

  return (
    <div className='flex flex-col items-center justify-center'>
      <h1 className='text-center uppercase text-[28px] font-bold text-text-icon-high-emphasis'>
        {translate('label_ops')}
      </h1>
      <p className='text-sm mt-4 text-center'>{translate('expired_text')}</p>
      <Link to={ROUTES.LOGIN}>
        <Button className='uppercase mt-16' variant='borderless'>
          {translate('btn_go_to_login')}
        </Button>
      </Link>
    </div>
  )
}
