import type { QueryClient } from '@tanstack/react-query'
import type { ActionFunctionArgs } from 'react-router'
import { validateSecurityTokenQuery } from '~/presentation/querys/validate-security-token-query'

export const resetPasswordLoader =
  (queryClient: QueryClient) =>
  async ({ request }: ActionFunctionArgs) => {
    const url = new URL(request.url)
    const securityToken = url.searchParams.get('securityToken')

    if (!securityToken) return { error: 'SecurityToken was not found' }

    await queryClient.ensureQueryData(
      validateSecurityTokenQuery({ securityToken })
    )

    return null
  }
