import { render, screen, fireEvent } from '@testing-library/react'
import { ConfirmModal } from './confirm-modal'
import { describe, it, expect, vi } from 'vitest'

describe('ConfirmModal', () => {
  const defaultProps = {
    title: 'Confirm Action',
    description: 'Are you sure you want to proceed?',
    btnSecondaryText: 'Cancel',
    btnPrimaryText: 'Confirm',
    isModalOpen: true,
    setOpenModal: vi.fn(),
    handleResetAndClose: vi.fn()
  }

  it('renders the modal with correct title and description', () => {
    render(<ConfirmModal {...defaultProps} />)

    expect(screen.getByText('Confirm Action')).toBeInTheDocument()
    expect(
      screen.getByText('Are you sure you want to proceed?')
    ).toBeInTheDocument()
  })

  it('renders the correct button texts', () => {
    render(<ConfirmModal {...defaultProps} />)

    expect(screen.getByText('Cancel')).toBeInTheDocument()
    expect(screen.getByText('Confirm')).toBeInTheDocument()
  })

  it('calls setOpenModal when secondary button is clicked', () => {
    render(<ConfirmModal {...defaultProps} />)

    fireEvent.click(screen.getByText('Cancel'))
    expect(defaultProps.setOpenModal).toHaveBeenCalledWith(false)
  })

  it('calls handleResetAndClose and setOpenModal when primary button is clicked', () => {
    render(<ConfirmModal {...defaultProps} />)

    fireEvent.click(screen.getByText('Confirm'))
    expect(defaultProps.handleResetAndClose).toHaveBeenCalled()
    expect(defaultProps.setOpenModal).toHaveBeenCalledWith(false)
  })

  it('does not render the modal when isModalOpen is false', () => {
    render(<ConfirmModal {...defaultProps} isModalOpen={false} />)

    expect(screen.queryByText('Confirm Action')).not.toBeInTheDocument()
  })

  it('works correctly without handleResetAndClose prop', () => {
    const propsWithoutReset = {
      ...defaultProps,
      handleResetAndClose: undefined
    }
    render(<ConfirmModal {...propsWithoutReset} />)

    fireEvent.click(screen.getByText('Confirm'))
    expect(defaultProps.setOpenModal).toHaveBeenCalledWith(false)
  })
})
