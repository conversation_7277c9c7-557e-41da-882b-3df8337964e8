import {
  Modal,
  ModalContent,
  ModalDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  Button
} from 'softo-design-system'

type Props = {
  title: string
  description: string
  btnSecondaryText: string
  btnPrimaryText: string
  isModalOpen: boolean
  setOpenModal: (open: boolean) => void
  handleResetAndClose?: () => void
}

export const ConfirmModal = ({
  title,
  description,
  btnSecondaryText,
  btnPrimaryText,
  isModalOpen,
  setOpenModal,
  handleResetAndClose
}: Props) => {
  const handleBtnPrimary = () => {
    if (handleResetAndClose) {
      handleResetAndClose()
    }
    setOpenModal(false)
  }

  return (
    <Modal open={isModalOpen} onOpenChange={setOpenModal} modal>
      <ModalContent className='bg-white md:w-[416px] max-w-[360px]'>
        <ModalHeader>
          <ModalTitle className='uppercase text-sm font-medium'>
            {title}
          </ModalTitle>
        </ModalHeader>

        <ModalDescription className='font-normal text-base p-6 max-w-[330px] m-auto'>
          {description}
        </ModalDescription>

        <ModalFooter className='flex gap-6 flex-row justify-end'>
          <Button
            variant='ghost'
            className='w-[100px]'
            onClick={() => setOpenModal(false)}
          >
            {btnSecondaryText}
          </Button>
          <Button className='w-[100px]' onClick={handleBtnPrimary}>
            {btnPrimaryText}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
