import {
  But<PERSON>,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem
} from 'softo-design-system'
import { TriangleDownIcon } from '@radix-ui/react-icons'
import { useTranslation } from '~/presentation/hooks/use-translation'
import type { AvailableLanguages } from '~/presentation/types'
import { availableLanguages } from '~/main/config/i18n/i18next-setup'
import { cn } from '~/presentation/utils/cn'

type Props = {
  classNameButton?: string
}

export const DropdownLanguage = ({ classNameButton }: Props) => {
  const { updateLanguage, language } = useTranslation()

  const handleChangeLanguage = (lng: AvailableLanguages) => {
    updateLanguage(lng)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          className={cn(
            'border-border-stroke-default text-text-icon-high-emphasis bg-transparent border w-26 uppercase font-normal',
            classNameButton
          )}
          variant='neutral'
        >
          {language} <TriangleDownIcon className='ml-2 w-4 h-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        sideOffset={0}
        className='w-24 p-0 bg-white rounded-none border-t-0 text-text-icon-high-emphasis top-0 shadow-none'
      >
        {(availableLanguages as AvailableLanguages[]).map((language) => (
          <DropdownMenuItem
            key={language}
            onClick={() => handleChangeLanguage(language)}
            className='cursor-pointer uppercase flex justify-center py-2 hover:bg-action-primary-active-16'
          >
            {language}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
