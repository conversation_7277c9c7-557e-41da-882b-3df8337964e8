import {
  Pagination,
  PaginationContent,
  Pagination<PERSON>llipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  Button
} from 'softo-design-system'

import { TriangleDownIcon } from '@radix-ui/react-icons'
import type { Paginated } from '~/main/types/paginated-type'
import { PAGESIZE_OPTIONS } from './constants/pagesize-options'
import { When } from '../when/when'

type Props = {
  paginatedData: Pick<Paginated<unknown>, 'pageSize' | 'pageCount' | 'page'>
  onChangePageSize: (newPageSize: number) => void
  onChangePage: (newPage: number) => void
}

export const TablePagination = ({
  paginatedData,
  onChangePageSize,
  onChangePage
}: Props) => {
  if (!paginatedData) return

  const { pageSize, pageCount, page } = paginatedData

  return (
    <div className='flex justify-between mt-10 py-4 w-full border-0 border-t ml-auto border-border-stroke-default'>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='outline'>
            {pageSize} <TriangleDownIcon className='ml-2 w-4 h-4' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className='bg-palettes-neutral-95 text-text-icon-high-emphasis w-20 min-w-max'>
          {PAGESIZE_OPTIONS.map((option) => (
            <DropdownMenuItem
              key={option}
              onClick={() => onChangePageSize(option)}
              className='cursor-pointer uppercase flex justify-center'
            >
              {option}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      <Pagination className='justify-end'>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              className='cursor-pointer'
              onClick={() => onChangePage(page > 1 ? page - 1 : 1)}
            />
          </PaginationItem>
          <PaginationItem>
            <PaginationLink
              className='cursor-pointer'
              isActive={page === 1}
              onClick={() => onChangePage(1)}
            >
              1
            </PaginationLink>
          </PaginationItem>
          <When condition={pageCount > 1}>
            <PaginationItem>
              <PaginationLink
                className='cursor-pointer'
                isActive={page === 2}
                onClick={() => onChangePage(2)}
              >
                2
              </PaginationLink>
            </PaginationItem>
          </When>
          <When condition={pageCount > 2}>
            <PaginationItem>
              <PaginationLink
                className='cursor-pointer'
                isActive={page === 3}
                onClick={() => onChangePage(3)}
              >
                3
              </PaginationLink>
            </PaginationItem>
          </When>
          <PaginationItem>
            <PaginationEllipsis />
          </PaginationItem>
          <PaginationItem>
            <PaginationLink>{pageCount}</PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationNext
              className='cursor-pointer'
              onClick={() =>
                onChangePage(page < pageCount ? page + 1 : pageCount)
              }
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  )
}
