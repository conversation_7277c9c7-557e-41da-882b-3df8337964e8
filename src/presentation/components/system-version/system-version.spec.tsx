import { screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { SystemVersion } from './system-version'
import { render } from '~/tests'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import type { ReactNode } from 'react'

vi.mock('../../../../package.json', () => ({
  version: '2.0.0'
}))

vi.mock('@tanstack/react-query', async () => {
  const originalModule = await vi.importActual('@tanstack/react-query')
  return {
    ...originalModule,
    useQuery: vi.fn().mockReturnValue({
      data: { appVersion: '1.0.0' }
    })
  }
})

describe('SystemVersion', () => {
  const queryClient = new QueryClient()

  const renderWithQueryClient = (ui: ReactNode) => {
    return render(
      <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>
    )
  }

  it('Should render the system version correctly', async () => {
    renderWithQueryClient(<SystemVersion />)

    const appVersion = await screen.findByText('1.0.0')
    const packageVersion = await screen.findByText('2.0.0')

    waitFor(() => {
      expect(appVersion).toBeInTheDocument()
      expect(packageVersion).toBeInTheDocument()
    })
  })
})
