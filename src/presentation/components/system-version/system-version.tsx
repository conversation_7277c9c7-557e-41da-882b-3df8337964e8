import { Icons } from 'softo-design-system'
import { BadgeEnvironment } from '../badge-environment/badge-environment'
import { version } from '../../../../package.json'
import { useQuery } from '@tanstack/react-query'
import { loadTenantQuery } from '~/main/layouts/auth-layout/querys/load-tenant-query'
import { When } from '../when/when'
import { DISABLE_VERSION_API, DISABLE_VERSION_FRONT } from '~/main/env'

type Props = {
  disabledBadgeEnvironment?: boolean
  stylesContainerVersions?: string
  stylesTextVersions?: string
  stylesContainer?: string
}

export const SystemVersion = ({
  disabledBadgeEnvironment,
  stylesContainerVersions,
  stylesTextVersions,
  stylesContainer
}: Props) => {
  const { data: tenant } = useQuery({ ...loadTenantQuery() })

  return (
    <div className={stylesContainer}>
      <When condition={!disabledBadgeEnvironment}>
        <BadgeEnvironment variant='inverted' />
      </When>
      <div className={stylesContainerVersions}>
        <When condition={!DISABLE_VERSION_API}>
          <div className='flex items-center mt-1 justify-center'>
            <div className='text-text-icon-low-emphasis'>
              <Icons
                icon='settings'
                variant='outlined'
                size={16}
                wrapperClassname='flex items-center'
              />
            </div>
            <span
              className={`flex ml-2 text-text-icon-low-emphasis text-xs ${stylesTextVersions}`}
            >
              {tenant?.appVersion}
            </span>
          </div>
        </When>
        <When condition={!DISABLE_VERSION_FRONT}>
          <div className='flex items-center justify-center'>
            <div className='text-text-icon-low-emphasis'>
              <Icons
                icon='computer'
                size={16}
                wrapperClassname='flex items-center'
              />
            </div>
            <span
              className={`flex ml-2 text-text-icon-low-emphasis text-xs ${stylesTextVersions}`}
            >
              {version}
            </span>
          </div>
        </When>
      </div>
    </div>
  )
}
