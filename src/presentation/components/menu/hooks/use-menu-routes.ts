import type { IconsProps } from 'softo-design-system'
import { useQuery } from '@tanstack/react-query'

import { ROUTES } from '~/main/types'
import { loadAccountMeQuery } from '~/presentation/querys/load-account-me-query'

export enum DASHBOARD_MENU_OPTIONS {
  Home = 'home',
  Users = 'users',
  Admins = 'admins',
  PermissionsGroup = 'permissions_group'
}

export type MenuItem = {
  title: DASHBOARD_MENU_OPTIONS
  path: string
  iconName: IconsProps['icon']
  variant: IconsProps['variant']
  enabled?: boolean
}

type MenuDashboard = {
  administrators: MenuItem[]
}

export const useMenuRoutes = () => {
  const { data } = useQuery(loadAccountMeQuery())

  const enableProfilePermissions = data?.permissions?.find(
    (permission) => permission.slug === 'view-permission-profile'
  )

  const menuRoutes: MenuDashboard = {
    administrators: [
      {
        title: DASHBOARD_MENU_OPTIONS.PermissionsGroup,
        iconName: 'stars',
        variant: 'filled',
        path: ROUTES.PERMISSIONS_PROFILE,
        enabled: !!enableProfilePermissions
      }
    ]
  }

  return { menuRoutes }
}
