import { cn } from '~/presentation/utils/cn'
import { Icons } from 'softo-design-system'
import { MenuOptions } from './components/menu-options/menu-options'
import { SystemVersion } from '../system-version/system-version'

type Props = {
  isOpen: boolean
  toggleMenu: () => void
}

export const Menu = ({ isOpen, toggleMenu }: Props) => {
  return (
    <div
      className={cn(
        'relative left-0 flex h-screen flex-col items-start justify-start bg-palettes-neutral-black pt-11 transition-all duration-300 ease-in-out overflow-hidden',
        {
          'w-16': !isOpen,
          'w-60': isOpen
        }
      )}
      data-testid='menu'
    >
      <button
        className='text-white mb-16 px-5'
        type='button'
        onClick={toggleMenu}
        data-testid='button-menu'
      >
        {isOpen ? <Icons icon='menu_open' /> : <Icons icon='menu' />}
      </button>

      <MenuOptions isOpen={isOpen} />

      <SystemVersion
        disabledBadgeEnvironment
        stylesContainer='flex mt-auto items-center justify-center w-full pb-4'
        stylesContainerVersions='flex flex-col gap-2'
      />
    </div>
  )
}
