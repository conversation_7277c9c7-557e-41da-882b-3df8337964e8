import { NavLink } from 'react-router'
import { cn } from '~/presentation/utils/cn'
import { useTranslation } from '~/presentation/hooks/use-translation'
import type { MenuItem } from '../../hooks/use-menu-routes'
import { Icons } from 'softo-design-system'

type Props = {
  title: string
  isOpen?: boolean
  routes: MenuItem[]
  onClickOption?: () => void
}

export const GroupItems = ({ title, isOpen, routes, onClickOption }: Props) => {
  const { translate } = useTranslation('common')

  if (routes?.length === 1 && routes?.find((route) => !route.enabled)) {
    return null
  }

  return (
    <nav className='flex flex-col gap-6 my-8 px-5 w-full'>
      <h3
        className={cn('text-palettes-neutral-50 mb-2 text-sm', {
          hidden: !isOpen
        })}
      >
        {translate(title)}
      </h3>
      {routes.map(({ title, iconName, path, variant, enabled }) => {
        if (!enabled) return null
        return (
          <NavLink
            key={path}
            to={path}
            onClick={onClickOption}
            data-testid={`navlink-menu-page-${title}`}
            className='flex items-center h-6 gap-3 text-white'
          >
            <div className='flex items-center'>
              <Icons
                icon={iconName}
                variant={variant}
                wrapperClassname='flex items-center'
              />
            </div>
            <span
              data-testid={`navlink-span-${title}`}
              className={cn(
                'opacity-100 transition-all duration-300 ease-in-out text-sm whitespace-nowrap',
                {
                  'opacity-0': !isOpen
                }
              )}
            >
              {translate(`${title}`)}
            </span>
          </NavLink>
        )
      })}
    </nav>
  )
}
