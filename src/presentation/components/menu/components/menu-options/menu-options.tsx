import {
  DASHBOARD_MENU_OPTIONS,
  useMenuRoutes
} from '../../hooks/use-menu-routes'
import { NavLink } from 'react-router'
import { ROUTES } from '~/main/types'
import { GroupItems } from '../../components/group-items/group-items'
import { Icons } from 'softo-design-system'
import { cn } from '~/presentation/utils/cn'
import { useTranslation } from '~/presentation/hooks/use-translation'

type Props = {
  isOpen?: boolean
  onClickOption?: () => void
}

export const MenuOptions = ({ isOpen, onClickOption }: Props) => {
  const { translate } = useTranslation()

  const { menuRoutes } = useMenuRoutes()

  return (
    <>
      <nav className='flex flex-col gap-4 mb-6 px-5 w-full'>
        <NavLink
          to={ROUTES.HOME}
          onClick={onClickOption}
          data-testid={`navlink-menu-page-${DASHBOARD_MENU_OPTIONS.Home}`}
          className='flex items-center h-6 gap-3 text-white'
        >
          <div className='flex items-center'>
            <Icons
              icon='home'
              variant='outlined'
              wrapperClassname='flex items-center'
            />
          </div>
          <span
            data-testid='navlink-span-home'
            className={cn(
              'opacity-100 transition-all duration-300 ease-in-out text-sm',
              {
                'opacity-0': !isOpen
              }
            )}
          >
            {translate(`${DASHBOARD_MENU_OPTIONS.Home}`)}
          </span>
        </NavLink>
      </nav>

      <div className='w-full px-2'>
        <hr className='w-full border-text-icon-medium-emphasis' />
      </div>

      <GroupItems
        isOpen={isOpen}
        routes={menuRoutes.administrators}
        title='admin_label'
        onClickOption={onClickOption}
      />
    </>
  )
}
