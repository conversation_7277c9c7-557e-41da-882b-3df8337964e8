import { fireEvent, screen } from '~/tests'
import { describe, it, expect, vi } from 'vitest'
import { render } from '@testing-library/react'
import { Menu } from './menu'
import type { permission } from 'process'

// Mock the useTranslation hook
vi.mock('~/presentation/hooks', () => ({
  useTranslation: vi.fn(() => ({
    translate: vi.fn((key) => key) // Retorna a chave para teste
  }))
}))

vi.mock('@tanstack/react-query', async () => {
  const originalModule = await vi.importActual('@tanstack/react-query')
  return {
    ...originalModule,
    useQuery: vi.fn().mockReturnValue({
      data: {
        id: 'f7989637-f9e0-4f8a-9992-c1301adfa50f',
        firstName: 'John Due',
        lastName: 'Due',
        email: '<EMAIL>',
        permissions: [
          {
            id: '3cece89f-e33c-4771-8fa5-5f08129708ea',
            name: 'Edit Permission Profile',
            slug: 'edit-permission-profile',
            description: 'Edit Permission Profile'
          },
          {
            id: '3f87470b-fb4e-4216-baba-2275c805000c',
            name: 'Create Permission Profile',
            slug: 'create-permission-profile',
            description: 'Create Permission Profile'
          },
          {
            id: '9f81ef20-9949-4061-8557-5cf95165d140',
            name: 'View Permission Profile',
            slug: 'view-permission-profile',
            description: 'View Permission Profile'
          },
          {
            id: 'a3883363-6cb9-4bb1-89c6-9181157891eb',
            name: 'Manager User Permission Profile',
            slug: 'manager-user-permission-profile',
            description: 'Manager User Permission Profile'
          }
        ]
      }
    })
  }
})

// Mock menuRoutes
vi.mock('./hooks', () => ({
  useMenuRoutes: vi.fn(() => ({
    menuRoutes: {
      administrators: [
        {
          title: 'permissions_group',
          iconName: 'stars',
          variant: 'filled',
          path: '/permissions-group'
        }
      ]
    },
    DASHBOARD_MENU_OPTIONS: {
      Home: 'home',
      Users: 'users',
      Admins: 'admins',
      PermissionsGroup: 'permissions_group'
    }
  }))
}))

// Mock NavLink from react-router-dom
vi.mock('react-router', () => {
  const actual = vi.importActual('react-router')
  return {
    ...actual,
    NavLink: ({ children, to, ...props }: any) => (
      <a href={to} {...props}>
        {children}
      </a>
    )
  }
})

// Mock MenuOpenOutlined and MenuOutlined
vi.mock('~/presentation/components', () => ({
  MenuOpenOutlined: () => (
    <span data-testid='menu-open-icon'>MenuOpenIcon</span>
  ),
  MenuOutlined: () => <span data-testid='menu-closed-icon'>MenuClosedIcon</span>
}))

describe('Menu Component', () => {
  it('renders correctly', () => {
    const toggleMenu = vi.fn()
    render(<Menu isOpen={false} toggleMenu={toggleMenu} />)

    const menuElement = screen.getByTestId('menu')

    expect(menuElement).toBeInTheDocument()
  })

  it('button toggles menu', () => {
    const toggleMenu = vi.fn()
    const { getByTestId, rerender } = render(
      <Menu isOpen={false} toggleMenu={toggleMenu} />
    )
    const buttonElement = getByTestId('button-menu')
    const menuElement = getByTestId('menu')

    expect(menuElement).toHaveClass('w-16') // Menu deve estar fechado inicialmente

    // Simulate clicking the button to toggle the menu open
    fireEvent.click(buttonElement)
    expect(toggleMenu).toHaveBeenCalled()

    // Rerender the component with the menu open
    rerender(<Menu isOpen={true} toggleMenu={toggleMenu} />)
    expect(menuElement).toHaveClass('w-60') // Menu deve estar aberto após o clique

    // Simulate clicking the button to toggle the menu closed again
    fireEvent.click(buttonElement)
    expect(toggleMenu).toHaveBeenCalledTimes(2)

    // Rerender the component with the menu closed
    rerender(<Menu isOpen={false} toggleMenu={toggleMenu} />)
    expect(menuElement).toHaveClass('w-16') // Menu deve estar fechado após o clique
  })

  it('renders navigation links', () => {
    const toggleMenu = vi.fn()
    render(<Menu isOpen={false} toggleMenu={toggleMenu} />)
    const navLinkElementHome = screen.getByTestId('navlink-menu-page-home')
    const navLinkSpanHome = screen.getByTestId('navlink-span-home')

    const navLinkElementPermissionsGroup = screen.getByTestId(
      'navlink-menu-page-permissions_group'
    )
    const navLinkSpanPermissionsGroup = screen.getByTestId(
      'navlink-span-permissions_group'
    )

    expect(navLinkElementHome).toBeInTheDocument()
    expect(navLinkSpanHome).toBeInTheDocument()

    expect(navLinkElementPermissionsGroup).toBeInTheDocument()
    expect(navLinkSpanPermissionsGroup).toBeInTheDocument()
  })

  it('toggle menu transitions opacity', () => {
    const toggleMenu = vi.fn()
    render(<Menu isOpen={false} toggleMenu={toggleMenu} />)

    const buttonElement = screen.getByTestId('button-menu')
    const navLinkSpan = screen.getByTestId('navlink-span-home')

    expect(navLinkSpan).toHaveClass('opacity-0') // Span deve estar invisível inicialmente

    // Simulate clicking the button to toggle the menu open
    fireEvent.click(buttonElement)

    setTimeout(() => {
      expect(navLinkSpan).not.toHaveClass('opacity-0') // Span deve estar visível após a transição
    }, 500) // Aguarda um intervalo de tempo suficiente para a transição

    // Simulate clicking the button to toggle the menu closed again
    fireEvent.click(buttonElement)

    setTimeout(() => {
      expect(navLinkSpan).toHaveClass('opacity-0') // Span deve estar invisível após a transição
    }, 500) // Aguarda um intervalo de tempo suficiente para a transição
  })
})
