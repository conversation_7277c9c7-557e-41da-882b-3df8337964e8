import { screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { Head<PERSON> } from './header'
import { render } from '~/tests'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import type { ReactNode } from 'react'

// Mock the useTranslation hook
vi.mock('~/presentation/hooks', () => ({
  useTranslation: () => ({
    translate: vi.fn(() => 'translated text')
  })
}))

// Mock the useBucketImage hook
vi.mock('~/presentation/hooks/use-bucket-image', () => ({
  useBucketImage: (imageName: string) =>
    `https://example.com/images/${imageName}`
}))

vi.mock('./components', () => ({
  DropdownLanguages: ({ dataTestId }: { dataTestId: string }) => (
    <div data-testid={dataTestId}>Languages</div>
  ),
  DropdownProfile: ({ dataTestId }: { dataTestId: string }) => (
    <div data-testid={dataTestId}>Profile</div>
  )
}))

vi.mock('./helpers', () => ({
  handleFormatNodeEnv: (env: string) => `Formatted ${env}`
}))

vi.mock('@tanstack/react-query', async () => {
  const originalModule = await vi.importActual('@tanstack/react-query')
  return {
    ...originalModule,
    useQuery: vi.fn().mockReturnValue({
      data: { firstName: 'John', lastName: 'Doe' }
    }),
    useMutation: vi.fn().mockReturnValue({
      mutate: vi.fn()
    })
  }
})

describe('Header', () => {
  const queryClient = new QueryClient()

  const renderWithQueryClient = (ui: ReactNode) => {
    return render(
      <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>
    )
  }

  beforeAll(() => {
    // Mock the NODE_ENV variable
    vi.stubEnv('NODE_ENV', 'development')

    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn()
      }))
    })
  })

  afterAll(() => {
    // Restore the original NODE_ENV value
    vi.unstubAllEnvs()
  })

  it('Should render the header correctly', async () => {
    const { container } = renderWithQueryClient(<Header />)

    const header = container.querySelector('.bg-palettes-primary-10')
    const logo = await screen.findByAltText('Goal logo')
    const badgeEnvironment = screen.findByText('Dev')
    const dropdownButton = await screen.findByRole('button', {
      name: /John Doe/
    })

    waitFor(() => {
      expect(header).toBeInTheDocument()
      expect(logo).toBeInTheDocument()
      expect(badgeEnvironment).toBeInTheDocument()
      expect(dropdownButton).toBeInTheDocument()
    })
  })
})
