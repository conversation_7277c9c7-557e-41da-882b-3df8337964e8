import { useState } from 'react'
import { Image } from '~/presentation/components/image/image'
import LogoGoalMobile from '/assets/images/logo-goal-mobile.svg'
import { DropdownUser } from './components/dropdown-user/dropdown-user'
import { DropdownLanguage } from '~/presentation/components/dropdown-language/dropdown-language'
import { MenuOptions } from '../menu/components/menu-options/menu-options'
import { SystemVersion } from '../system-version/system-version'
import { BadgeEnvironment } from '../badge-environment/badge-environment'
import {
  Drawer,
  DrawerTrigger,
  DrawerContent,
  DrawerClose,
  Icons
} from 'softo-design-system'

export const Header = () => {
  // const logoUrl = useBucketImage('softo-logo.svg')

  const [isDrawerOpen, setIsDrawerOpen] = useState(false)

  const handleDrawerClose = () => {
    setIsDrawerOpen(false)
  }

  return (
    <header className='flex items-center justify-between p-8 bg-palettes-primary-10'>
      <div className='md:hidden'>
        <Drawer
          direction='left'
          open={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
        >
          <DrawerTrigger onClick={(prev) => setIsDrawerOpen(!prev)}>
            <Icons icon='menu' className='text-white' />
          </DrawerTrigger>
          <DrawerContent className='bg-palettes-neutral-black h-full w-16 border-0 pt-6 rounded-none'>
            <DrawerClose>
              <MenuOptions onClickOption={handleDrawerClose} />
            </DrawerClose>
            <SystemVersion
              disabledBadgeEnvironment
              stylesContainer='flex mt-auto items-center justify-center w-full pb-4'
              stylesContainerVersions='flex flex-col gap-2'
            />
          </DrawerContent>
        </Drawer>
      </div>

      <div className='flex'>
        <Image
          layout='constrained'
          width={120}
          height={40}
          src={LogoGoalMobile}
          alt='Goal logo'
        />

        <div className='flex items-center ml-4'>
          <BadgeEnvironment
            variant='outline'
            className='text-white border border-white bg-transparent'
          />
        </div>
      </div>

      <div className='flex gap-6'>
        <div className='hidden md:block'>
          <DropdownLanguage classNameButton='text-white' />
        </div>

        <DropdownUser />
      </div>
    </header>
  )
}
