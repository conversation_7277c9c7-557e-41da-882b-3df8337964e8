import { TriangleDownIcon } from '@radix-ui/react-icons'

import { useMutation, useQuery } from '@tanstack/react-query'
import { makeAccountLogoutService } from '~/application/usecases/auth/logout'
import { useNavigate } from 'react-router'
import { ROUTES } from '~/main/types'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { useMediaQuery } from '~/presentation/hooks/use-media-query'
import { getInitials } from '~/presentation/utils/get-initials'
import { loadAccountMeQuery } from '~/presentation/querys/load-account-me-query'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  Icons
} from 'softo-design-system'

export const DropdownUser = () => {
  const { translate } = useTranslation('common')
  const navigate = useNavigate()
  const { data } = useQuery(loadAccountMeQuery())
  const accountLogoutService = makeAccountLogoutService()
  const isMobile = useMediaQuery('(max-width:768px)')

  const { mutate: logout } = useMutation({
    mutationFn: () => accountLogoutService.execute(),
    onSuccess: () => navigate(ROUTES.LOGIN)
  })

  const fullName = `${data?.firstName} ${data?.lastName}`

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className='flex items-center text-white outline-0'>
          {isMobile ? getInitials(fullName) : fullName}{' '}
          <TriangleDownIcon className='ml-2 w-4 h-4' />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-48 bg-palettes-neutral-95 text-text-icon-high-emphasis'>
        <DropdownMenuItem className='cursor-pointer uppercase flex justify-center'>
          <button
            type='button'
            className='flex items-center w-full'
            data-testid='button-logout'
            onClick={() => logout()}
          >
            <Icons
              icon='exit_to_app'
              className='text-text-icon-medium-emphasis mr-2'
              wrapperClassname='flex items-center'
            />
            <span className='uppercase text-text-icon-high-emphasis font-semibold'>
              {translate('label_logout')}
            </span>
          </button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
