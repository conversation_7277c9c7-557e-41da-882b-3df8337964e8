import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { DropdownUser } from './dropdown-user'
import type { ReactNode } from 'react'

const navigate = vi.fn()

vi.mock('~/presentation/hooks', () => ({
  useTranslation: vi.fn(() => ({
    translate: vi.fn((key) => key)
  }))
}))

vi.mock('softo-design-system', async () => {
  const originalModule = await vi.importActual('softo-design-system')
  return {
    ...originalModule,
    DropdownMenu: ({ children }: { children: ReactNode }) => (
      <div>{children}</div>
    ),
    DropdownMenuContent: ({ children }: { children: ReactNode }) => (
      <div>{children}</div>
    ),
    DropdownMenuTrigger: ({
      children,
      onClick
    }: { children: ReactNode; onClick: () => void }) => (
      <div onClick={onClick}>{children}</div>
    ),
    DropdownMenuItem: ({ children }: { children: ReactNode }) => (
      <div>{children}</div>
    )
  }
})

vi.mock('react-router', () => ({
  useNavigate: () => navigate
}))

vi.mock('@tanstack/react-query', async () => {
  const originalModule = await vi.importActual('@tanstack/react-query')
  return {
    ...originalModule,
    useQuery: vi.fn().mockReturnValue({
      data: { firstName: 'John', lastName: 'Doe' }
    })
  }
})

describe('DropdownUser', () => {
  const queryClient = new QueryClient()

  const renderWithQueryClient = (ui: ReactNode) => {
    return render(
      <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>
    )
  }

  beforeAll(() => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn()
      }))
    })
  })

  it('should render the user name and trigger the logout mutation on button click', async () => {
    renderWithQueryClient(<DropdownUser />)

    const userNameButton = await screen.findByText('John Doe')
    expect(userNameButton).toBeInTheDocument()

    fireEvent.click(userNameButton)

    const logoutButton = await screen.findByTestId('button-logout')
    expect(logoutButton).toBeInTheDocument()

    // Simulate click to logout
    fireEvent.click(logoutButton)

    waitFor(async () => {
      const loginTitle = await screen.findByText('login_title')
      expect(loginTitle).toBeInTheDocument()
    })
  })
})
