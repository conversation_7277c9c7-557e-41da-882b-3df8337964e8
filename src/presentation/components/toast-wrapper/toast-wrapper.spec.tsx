import { render, screen } from '@testing-library/react'
import { ToastWrapper } from './toast-wrapper'
import { describe, it, expect, vi } from 'vitest'

type ToastProps = {
  description: string
  open: boolean
  variant: 'success' | 'error'
  icon: 'success' | 'error'
  setOpen: (open: boolean) => void
}

vi.mock('softo-design-system', () => ({
  Toast: ({ description, open, variant, icon, setOpen }: ToastProps) =>
    open ? (
      <div data-testid='mock-toast' data-variant={variant} data-icon={icon}>
        {description}
        <button onClick={() => setOpen(false)}>Close</button>
      </div>
    ) : null
}))

describe('ToastWrapper', () => {
  const defaultProps = {
    toastVariant: 'success' as const,
    description: 'Test toast message',
    isToastOpen: true,
    onClose: vi.fn()
  }

  it('renders the Toast component when isToastOpen is true', () => {
    render(<ToastWrapper {...defaultProps} />)
    expect(screen.getByTestId('mock-toast')).toBeInTheDocument()
  })

  it('does not render the Toast component when isToastOpen is false', () => {
    render(<ToastWrapper {...defaultProps} isToastOpen={false} />)
    expect(screen.queryByTestId('mock-toast')).not.toBeInTheDocument()
  })

  it('passes the correct variant to the Toast component', () => {
    render(<ToastWrapper {...defaultProps} />)
    expect(screen.getByTestId('mock-toast')).toHaveAttribute(
      'data-variant',
      'success'
    )
  })

  it('passes the correct icon to the Toast component', () => {
    render(<ToastWrapper {...defaultProps} />)
    expect(screen.getByTestId('mock-toast')).toHaveAttribute(
      'data-icon',
      'success'
    )
  })

  it('renders the correct description in the Toast component', () => {
    render(<ToastWrapper {...defaultProps} />)
    expect(screen.getByText('Test toast message')).toBeInTheDocument()
  })

  it('calls onClose when the close button is clicked', () => {
    render(<ToastWrapper {...defaultProps} />)
    screen.getByText('Close').click()
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1)
  })

  it('renders with error variant correctly', () => {
    render(<ToastWrapper {...defaultProps} toastVariant='error' />)
    expect(screen.getByTestId('mock-toast')).toHaveAttribute(
      'data-variant',
      'error'
    )
    expect(screen.getByTestId('mock-toast')).toHaveAttribute(
      'data-icon',
      'error'
    )
  })
})
