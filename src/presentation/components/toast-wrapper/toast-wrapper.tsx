import { Toast } from 'softo-design-system'
import { When } from '../when/when'

type Props = {
  toastVariant: 'success' | 'error'
  description: string | React.ReactNode
  isToastOpen: boolean
  onClose: () => void
}

export const ToastWrapper = ({
  toastVariant,
  description,
  isToastOpen,
  onClose
}: Props) => {
  return (
    <When condition={isToastOpen}>
      <Toast
        viewportClassName='md:top-44 w-full md:min-w-max md:max-w-max md:pr-4'
        description={description}
        open={isToastOpen}
        variant={toastVariant}
        icon={toastVariant}
        setOpen={onClose}
      />
    </When>
  )
}
