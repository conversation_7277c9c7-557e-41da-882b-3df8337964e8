import { Icon<PERSON>, But<PERSON> } from 'softo-design-system'
import { useMediaQuery } from '~/presentation/hooks/use-media-query'
import { When } from '../when/when'
import { NavLink } from 'react-router'

type Props = {
  title: string
  btnText?: string
  previousPage?: string
}

export const PageTitle = ({ title, btnText, previousPage }: Props) => {
  const isMobile = useMediaQuery('(max-width: 768px)')

  return (
    <div className='flex flex-col justify-center w-full max-md:max-w-full'>
      <div className='flex gap-10 justify-between items-center w-full max-md:max-w-full'>
        <div className='flex items-center gap-6'>
          <When condition={!!previousPage}>
            <NavLink to={previousPage as string}>
              <Icons
                icon='arrow_back'
                size={24}
                className='text-palettes-primary-10'
                wrapperClassname='flex items-center'
              />
            </NavLink>
          </When>

          <h1 className='self-stretch my-auto uppercase text-xl md:text-[28px] font-bold text-text-icon-high-emphasis'>
            {title}
          </h1>
        </div>
        <When condition={!!btnText}>
          <Button className='w-10 h-10 md:w-max'>
            {isMobile ? (
              <Icons
                icon='add'
                className='text-white'
                wrapperClassname='flex items-center'
              />
            ) : (
              btnText
            )}
          </Button>
        </When>
      </div>
    </div>
  )
}
