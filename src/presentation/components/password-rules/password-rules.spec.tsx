import { vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { PasswordRules } from './password-rules'

const watch = vi.fn()

const makeSut = (password = '') => {
  watch.mockImplementation(() => password)
  render(<PasswordRules password={password} />)
}

describe('PasswordRules', () => {
  it('Should show error messages when password is not valid', async () => {
    makeSut('') // Password is empty to trigger error conditions
    const lowerCaseErrorIcon = await screen.findByTestId(
      'lower-case-error-icon'
    )
    const upperCaseErrorIcon = await screen.findByTestId(
      'upper-case-error-icon'
    )
    const specialCharacterErrorIcon = await screen.findByTestId(
      'special-character-error-icon'
    )
    const numberErrorIcon = await screen.findByTestId('number-error-icon')
    const minLengthErrorIcon = await screen.findByTestId(
      'min-length-error-icon'
    )

    expect(lowerCaseErrorIcon).toBeInTheDocument()
    expect(upperCaseErrorIcon).toBeInTheDocument()
    expect(specialCharacterErrorIcon).toBeInTheDocument()
    expect(numberErrorIcon).toBeInTheDocument()
    expect(minLengthErrorIcon).toBeInTheDocument()
  })

  it('Should show success messages when password is valid', async () => {
    makeSut('@Senha123') // A valid password to trigger success conditions
    const lowerCaseSuccessIcon = await screen.findByTestId(
      'lower-case-success-icon'
    )
    const upperCaseSuccessIcon = await screen.findByTestId(
      'upper-case-success-icon'
    )
    const specialCharacterSuccessIcon = await screen.findByTestId(
      'special-character-success-icon'
    )
    const numberSuccessIcon = await screen.findByTestId('number-success-icon')
    const minLengthSuccessIcon = await screen.findByTestId(
      'min-length-success-icon'
    )

    expect(lowerCaseSuccessIcon).toBeInTheDocument()
    expect(upperCaseSuccessIcon).toBeInTheDocument()
    expect(specialCharacterSuccessIcon).toBeInTheDocument()
    expect(numberSuccessIcon).toBeInTheDocument()
    expect(minLengthSuccessIcon).toBeInTheDocument()
  })
})
