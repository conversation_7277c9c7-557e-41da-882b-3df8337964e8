import { useTranslation } from '~/presentation/hooks/use-translation'
import { Cross2Icon, CheckIcon } from '@radix-ui/react-icons'
import { cn } from '~/presentation/utils/cn'

type Props = {
  password: string | undefined
}

export const PasswordRules = ({ password }: Props) => {
  const { translate } = useTranslation(['sign-up', 'validations'])

  const hasUpperCase = password?.toLowerCase() !== password
  const hasLowerCase = password?.toUpperCase() !== password
  const hasSpecialCharacter = /[ !"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]/.test(
    password ?? ''
  )
  const hasNumber = /\d/.test(password ?? '')
  const hasMinLength = password ? password?.length >= 8 : false

  return (
    <div className='flex flex-col items-start py-2 px-4 border border-palettes-neutral-90 rounded gap-2 mb-8'>
      <span className='text-xs font-semibold text-[#4D4D4D]'>
        {translate('password_rules_title')}
      </span>
      <span
        data-testid='lower-case'
        className={cn(
          'flex items-center text-xs font-semibold',
          hasLowerCase
            ? 'text-border-stroke-success'
            : 'text-text-icon-low-emphasis'
        )}
      >
        {hasLowerCase && (
          <CheckIcon
            className='w-4 h-4 mr-2'
            data-testid='lower-case-success-icon'
          />
        )}
        {!hasLowerCase && (
          <Cross2Icon
            className='w-4 h-4 mr-2'
            data-testid='lower-case-error-icon'
          />
        )}
        {translate('validations:password_rules_01')}
      </span>
      <span
        data-testid='upper-case'
        className={cn(
          'flex items-center text-xs font-semibold',
          hasUpperCase
            ? 'text-border-stroke-success'
            : 'text-text-icon-low-emphasis'
        )}
      >
        {hasUpperCase && (
          <CheckIcon
            className='w-4 h-4 mr-2'
            data-testid='upper-case-success-icon'
          />
        )}
        {!hasUpperCase && (
          <Cross2Icon
            className='w-4 h-4 mr-2'
            data-testid='upper-case-error-icon'
          />
        )}
        {translate('validations:password_rules_02')}
      </span>
      <span
        data-testid='special-character'
        className={cn(
          'flex items-center text-xs font-semibold',
          hasSpecialCharacter
            ? 'text-border-stroke-success'
            : 'text-text-icon-low-emphasis'
        )}
      >
        {hasSpecialCharacter && (
          <CheckIcon
            className='w-4 h-4 mr-2'
            data-testid='special-character-success-icon'
          />
        )}
        {!hasSpecialCharacter && (
          <Cross2Icon
            className='w-4 h-4 mr-2'
            data-testid='special-character-error-icon'
          />
        )}
        {translate('validations:password_rules_03')}
      </span>
      <span
        data-testid='number'
        className={cn(
          'flex items-center text-xs font-semibold',
          hasNumber
            ? 'text-border-stroke-success'
            : 'text-text-icon-low-emphasis'
        )}
      >
        {hasNumber && (
          <CheckIcon
            className='w-4 h-4 mr-2'
            data-testid='number-success-icon'
          />
        )}
        {!hasNumber && (
          <Cross2Icon
            className='w-4 h-4 mr-2'
            data-testid='number-error-icon'
          />
        )}
        {translate('validations:password_rules_04')}
      </span>
      <span
        data-testid='min-length'
        className={cn(
          'flex items-center text-xs font-semibold',
          hasMinLength
            ? 'text-border-stroke-success'
            : 'text-text-icon-low-emphasis'
        )}
      >
        {hasMinLength && (
          <CheckIcon
            className='w-4 h-4 mr-2'
            data-testid='min-length-success-icon'
          />
        )}
        {!hasMinLength && (
          <Cross2Icon
            className='w-4 h-4 mr-2'
            data-testid='min-length-error-icon'
          />
        )}
        {translate('validations:password_rules_05')}
      </span>
    </div>
  )
}
