import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  Icons,
  type IconsProps
} from 'softo-design-system'
import { useTranslation } from '~/presentation/hooks/use-translation'

export type ThreeDotsOption = {
  name: string
  action: () => void
  icon: IconsProps['icon']
}

type Props = {
  options: ThreeDotsOption[]
  i18nKey: string
}

export const ThreeDots = ({ options, i18nKey }: Props) => {
  const { translate } = useTranslation(i18nKey ?? 'common')

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className='outline-0'>
          <Icons icon='more_vert' className='text-palettes-neutral-50' />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        side='bottom'
        align='end'
        sideOffset={15}
        alignOffset={-5}
        className='w-48 bg-palettes-neutral-95 text-text-icon-high-emphasis flex flex-col'
      >
        <div className='absolute right-3 -top-1.5 w-3 h-3 bg-palettes-neutral-95 rotate-45 transform origin-center'></div>
        {options?.map((option) => (
          <DropdownMenuItem
            key={option.name}
            className='cursor-pointer uppercase justify-center flex items-center outline-none'
          >
            <button
              type='button'
              className='flex items-center w-full outline-0'
              onClick={option.action}
            >
              <Icons
                icon={option.icon}
                variant='outlined'
                size={18}
                className='text-text-icon-medium-emphasis mr-2'
                wrapperClassname='flex items-center'
              />
              <span className='uppercase text-text-icon-high-emphasis font-semibold text-xs'>
                {translate(option.name)}
              </span>
            </button>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
