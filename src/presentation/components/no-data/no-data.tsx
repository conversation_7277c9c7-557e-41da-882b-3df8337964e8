import { TableCell, TableRow } from 'softo-design-system'
import { useTranslation } from '~/presentation/hooks/use-translation'

type Props = {
  message?: string
  classNameRow?: string
  isTable?: boolean
}

export const NoData = ({ message, classNameRow, isTable = true }: Props) => {
  const { translate } = useTranslation()

  if (!isTable) {
    return (
      <div className={`w-full flex ${classNameRow}`}>
        <div className='w-full py-4'>
          <span className='text-base text-text-icon-low-emphasis block text-center w-full'>
            {translate(message ?? 'not_found_result')}
          </span>
        </div>
      </div>
    )
  }

  return (
    <TableRow className={`w-full flex ${classNameRow}`}>
      <TableCell className='w-full py-4'>
        <span className='text-base text-text-icon-low-emphasis block text-center w-full'>
          {translate(message ?? 'not_found_result')}
        </span>
      </TableCell>
    </TableRow>
  )
}
