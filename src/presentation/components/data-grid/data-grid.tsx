import { Table, TableBody } from 'softo-design-system'
import { TablePagination } from '~/presentation/components/table-pagination/table-pagination'
import { useEffect, useState, type ReactNode } from 'react'
import { cn } from '~/presentation/utils/cn'
import { useQuery } from '@tanstack/react-query'
import type { Paginated } from '~/main/types/paginated-type'
import { When } from '../when/when'

export type DataGridLoadQuery<T, U> = (
  params: {
    pageSize: number
    page: number
  } & U
) => {
  queryKey: (string | number)[]
  queryFn: () => Promise<T>
}

interface DataGridProps<T, U = {}> {
  children: ReactNode
  className?: string
  dataGridHead?: ReactNode
  loadQuery?: DataGridLoadQuery<T, U>
  disabledPagination?: boolean
}

export const DataGrid = <T,>({
  children,
  className,
  dataGridHead,
  disabledPagination,
  loadQuery
}: DataGridProps<T>) => {
  const [pageSize, setPageSize] = useState(10)
  const [page, setPage] = useState(1)

  const [isFirstLoad, setIsFirstLoad] = useState(true)

  const { data, refetch } = useQuery(
    loadQuery
      ? loadQuery({ pageSize, page })
      : { queryKey: [], queryFn: () => Promise.resolve({} as T) }
  )

  const handleChangePageSize = (newPageSize: number) => {
    setPageSize(newPageSize)
  }

  const handleChangePage = (newPage: number) => {
    setPage(newPage)
  }

  useEffect(() => {
    if (!isFirstLoad && (pageSize >= 10 || page >= 1)) {
      // Skip on first load
      refetch()
    }
    setIsFirstLoad(false) // Set to false after first load
  }, [pageSize, page])

  return (
    <>
      <Table className={cn(`w-full ${className}`)}>
        {dataGridHead}
        <TableBody className='block max-h-screen md:max-h-[calc(100vh-500px)] overflow-y-auto w-full custom-scrollbar'>
          {children}
        </TableBody>
      </Table>

      <When condition={!disabledPagination}>
        <TablePagination
          paginatedData={data as Paginated<T>}
          onChangePageSize={handleChangePageSize}
          onChangePage={handleChangePage}
        />
      </When>
    </>
  )
}
