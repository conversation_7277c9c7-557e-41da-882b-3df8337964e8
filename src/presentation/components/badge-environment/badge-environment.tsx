import { Badge, type BadgeProps } from 'softo-design-system'
import { NODE_ENV } from '~/main/env'

export const BadgeEnvironment = ({
  variant,
  className,
  ...props
}: BadgeProps) => {
  const labelEnvironment = {
    development: 'Dev',
    production: 'Prod',
    test: 'Test',
    staging: 'Stag'
  }

  return (
    <Badge variant={variant} className={className} {...props}>
      {labelEnvironment[NODE_ENV]}
    </Badge>
  )
}
