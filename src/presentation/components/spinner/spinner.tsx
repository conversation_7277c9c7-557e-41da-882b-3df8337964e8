import { cn } from '~/presentation/utils/cn'

type Props = {
  color?: string
  width?: string
  height?: string
}

export const Spinner = ({ color, width, height }: Props) => {
  return (
    <div className='flex justify-center items-center'>
      <div
        className={cn(
          `border-2 border-b-transparent border-l-transparent border-r-transparent rounded-full animate-spin ${color ? color : 'border-t-white'} ${width ? width : 'w-6'} ${height ? height : 'h-6'}`
        )}
      ></div>
    </div>
  )
}
