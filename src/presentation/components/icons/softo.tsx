export const SoftoIcon = () => (
  <svg
    width='180'
    height='48'
    viewBox='0 0 180 48'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      d='M25.8645 34.5496C28.0751 36.0699 30.6064 37.063 33.4584 37.5328C34.3594 37.6818 35.2032 37.7582 35.9896 37.7582C36.7761 37.7582 37.5168 37.6818 38.2117 37.5328C38.9065 37.3839 39.5327 37.1394 40.0977 36.8033C41.2965 36.0699 41.896 35.0194 41.896 33.652C41.896 32.4526 41.014 31.448 39.254 30.642C38.4675 30.2868 37.5664 29.9468 36.5547 29.6298L33.4622 28.6175C30.8164 27.7161 28.9952 26.8719 27.9911 26.085C26.987 25.2982 26.2463 24.4807 25.7691 23.6366C25.2918 22.7924 25.0513 21.7458 25.0513 20.5006C25.0513 19.2553 25.3453 18.1361 25.9371 17.1545C26.5288 16.169 27.3192 15.3477 28.3118 14.6907C30.2628 13.392 32.5192 12.7426 35.0886 12.7426C39.7007 12.7426 43.4117 13.7472 46.2255 15.7526L44.1447 19.664C41.2584 17.884 38.3033 16.9902 35.2871 16.9902C33.1682 16.9902 31.6792 17.6014 30.8164 18.8199C30.5338 19.2324 30.3964 19.7481 30.3964 20.3669C30.3964 20.9857 30.6331 21.5204 31.1142 21.9712C31.5914 22.4219 32.2252 22.8192 33.0117 23.1668C33.7982 23.5143 34.6992 23.8467 35.7109 24.1675L38.8034 25.1225C41.4264 26.0048 43.2475 26.849 44.2592 27.655C46.2255 29.2325 47.2105 31.1042 47.2105 33.2815C47.2105 36.1119 46.0193 38.3159 43.6407 39.8935C41.5218 41.2991 38.834 42.002 35.5697 42.002C32.1565 42.002 29.0716 41.3259 26.3189 39.9775C25.3987 39.5459 24.5855 39.0302 23.8716 38.4305L25.8645 34.5496Z'
      fill='#4D4D4D'
    />
    <path
      d='M54.6431 27.3201C54.6431 28.7818 54.9028 30.1353 55.4331 31.3805C55.9526 32.6257 56.6777 33.7084 57.5976 34.6288C59.5456 36.5453 62.024 37.4981 65.0218 37.4981C67.814 37.4981 70.1624 36.5453 72.0564 34.6288C73.9503 32.7556 74.8918 30.3952 74.8918 27.5691C74.8918 24.6024 73.907 22.1445 71.9373 20.1739C69.9893 18.2249 67.5109 17.2504 64.5131 17.2504C61.7209 17.2504 59.3725 18.2141 57.4785 20.1522C55.5954 22.0579 54.6431 24.4508 54.6431 27.3201ZM49.3076 27.6341C49.3076 25.5011 49.7297 23.5196 50.5738 21.7006C51.418 19.8815 52.5652 18.3007 54.0045 16.9689C57.0565 14.1537 60.7361 12.7461 65.0326 12.7461C69.3616 12.7461 72.9763 14.0887 75.855 16.774C78.7771 19.4917 80.2381 22.9891 80.2381 27.266C80.2381 31.4454 78.6689 34.9644 75.5412 37.8121C72.5001 40.6056 68.8313 42.0024 64.524 42.0024C60.1409 42.0024 56.5154 40.6706 53.6691 38.007C50.7578 35.3109 49.3076 31.8461 49.3076 27.6341Z'
      fill='#4D4D4D'
    />
    <path
      d='M104.681 11.7676C101.5 9.52542 96.14 9.97615 94.4258 13.3146C93.582 14.8884 93.162 17.2337 93.162 20.3468V21.7563H98.6408V26.0879H93.162V41.4395H87.7902V26.0879H82.5864V21.7563H87.7902V20.6867C87.7902 13.6698 90.0695 9.13198 94.6243 7.06932C96.1973 6.35502 97.8123 5.99979 99.4616 5.99979C102.18 5.99979 103.829 6.50781 105.948 7.52005L104.681 11.7676Z'
      fill='#4D4D4D'
    />
    <path
      d='M102.621 41.8047C101.571 41.8047 100.727 41.4876 100.09 40.8497C99.4521 40.2119 99.1353 39.3677 99.1353 38.3173C99.1353 37.2668 99.4521 36.4265 100.09 35.8C100.727 35.1698 101.571 34.8566 102.621 34.8566C103.671 34.8566 104.515 35.1698 105.152 35.8C105.79 36.4265 106.107 37.2668 106.107 38.3173C106.107 39.3677 105.79 40.2119 105.152 40.8497C104.515 41.4876 103.671 41.8047 102.621 41.8047Z'
      fill='#FF6600'
    />
    <path
      d='M109.952 17.6412H107.52V14.1499L113.862 8.94362H115.297V13.3058H123.341V17.6412H115.297V29.0088C115.297 32.1983 115.702 34.3755 116.507 35.5367C117.313 36.6827 118.89 37.3855 121.23 37.6491L120.413 41.4459C114.789 42.1602 111.46 39.9371 110.429 34.7766C110.109 33.2373 109.952 31.5795 109.952 29.7957V17.6412V17.6412Z'
      fill='#4D4D4D'
    />
    <path
      d='M130.541 27.3149C130.541 28.7779 130.804 30.1339 131.327 31.3791C131.85 32.6282 132.576 33.7092 133.492 34.6297C135.443 36.5434 137.917 37.4984 140.918 37.4984C143.713 37.4984 146.053 36.5396 147.947 34.6297C149.84 32.7542 150.787 30.4013 150.787 27.567C150.787 24.6029 149.802 22.1392 147.832 20.1682C145.881 18.2163 143.407 17.2422 140.41 17.2422C137.615 17.2422 135.271 18.2086 133.381 20.1414C131.488 22.0551 130.541 24.4463 130.541 27.3149ZM125.196 27.6281C125.196 25.4891 125.62 23.5104 126.46 21.6922C127.303 19.874 128.445 18.2965 129.892 16.9634C132.95 14.152 136.623 12.7426 140.918 12.7426C145.247 12.7426 148.855 14.0833 151.742 16.7648C154.666 19.4844 156.129 22.9833 156.129 27.2576C156.129 31.4402 154.563 34.9582 151.433 37.8078C148.393 40.6038 144.721 41.998 140.41 41.998C136.023 41.998 132.404 40.665 129.556 38.0026C126.65 35.3058 125.196 31.8451 125.196 27.6281Z'
      fill='#4D4D4D'
    />
  </svg>
)
