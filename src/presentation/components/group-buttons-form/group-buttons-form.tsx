import { Button } from 'softo-design-system'
import { Spinner } from '../spinner/spinner'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { useMediaQuery } from '~/presentation/hooks/use-media-query'

type GroupButtonsFormProps = {
  handleConfirmCancel: () => void
  isPending: boolean
}

export const GroupButtonsForm = ({
  handleConfirmCancel,
  isPending
}: GroupButtonsFormProps) => {
  const isMobile = useMediaQuery('(max-width:768px)')
  const { translate } = useTranslation('common')

  return (
    <div className='flex items-center justify-between mt-6 gap-4'>
      <Button
        size={isMobile ? 'default' : 'lg'}
        type='button'
        variant='ghost'
        className='w-[211px] uppercase'
        onClick={handleConfirmCancel}
      >
        {translate('btn_cancel')}
      </Button>
      <Button
        size={isMobile ? 'default' : 'lg'}
        type='submit'
        variant='default'
        className='w-[211px] uppercase'
      >
        {isPending ? <Spinner /> : translate('btn_save')}
      </Button>
    </div>
  )
}
