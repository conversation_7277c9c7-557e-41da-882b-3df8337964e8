import { forwardRef, useState } from 'react'
import {
  Icons,
  Input,
  type InputProps,
  InputMessage
} from 'softo-design-system'
import { cn } from '~/presentation/utils/cn'
import type { FieldError } from 'react-hook-form'
import { useTranslation } from '~/presentation/hooks/use-translation'

type Props = InputProps & {
  children?: React.ReactNode
  error?: FieldError | undefined
  wrapperClassname?: string
  isPassword?: boolean
}

export const TextInput = forwardRef<HTMLInputElement, Props>(
  ({ name, children, error, wrapperClassname, isPassword, ...props }, ref) => {
    const { translate } = useTranslation('validations')
    const [passwordType, setPasswordType] = useState<'password' | 'text'>(
      'password'
    )

    return (
      <div className={cn('mb-6', wrapperClassname)}>
        <Input
          ref={ref}
          name={name}
          isError={!!error}
          type={isPassword ? passwordType : undefined}
          inputAdornment={{
            right: isPassword ? (
              passwordType === 'password' ? (
                <button type='button' onClick={() => setPasswordType('text')}>
                  <Icons
                    icon='visibility'
                    className='text-text-icon-medium-emphasis'
                    wrapperClassname='flex items-center'
                  />
                </button>
              ) : (
                <button
                  type='button'
                  onClick={() => setPasswordType('password')}
                >
                  <Icons
                    icon='visibility_off'
                    className='text-text-icon-medium-emphasis'
                    wrapperClassname='flex items-center'
                  />
                </button>
              )
            ) : undefined
          }}
          {...props}
        />
        {children}
        {error && (
          <InputMessage
            variant='error'
            className='mt-2 text-left justify-start'
          >
            {translate(error?.message as string)}
          </InputMessage>
        )}
      </div>
    )
  }
)

TextInput.displayName = 'TextInput'
