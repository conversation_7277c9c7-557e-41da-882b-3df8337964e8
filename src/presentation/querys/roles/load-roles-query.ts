import { makeLoadRolesService } from '~/application/usecases/roles/load-roles'
import { createQueryFn } from '~/presentation/querys/helpers/create-query'

type Props = {
  pageSize?: number
  page?: number
}

export const queryKeyLoadRoles = 'loadRoles'

export const loadRolesQuery = (params?: Props) => {
  const loadRolesService = makeLoadRolesService()

  return {
    queryKey: [queryKeyLoadRoles],
    queryFn: createQueryFn(loadRolesService, {
      pageSize: params?.pageSize || 10,
      page: params?.page || 1
    })
  }
}
