import { makeLoadAccountMeService } from '~/application/usecases/auth/account-me'
import { createQueryFn } from '~/presentation/querys/helpers/create-query'

export const loadAccountMeQueryKey = ['loadAccountMe']

export const loadAccountMeQuery = () => {
  const loadAccountMeService = makeLoadAccountMeService()

  return {
    queryKey: loadAccountMeQueryKey,
    queryFn: createQueryFn(loadAccountMeService)
  }
}
