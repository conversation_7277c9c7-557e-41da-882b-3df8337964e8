import type { UseCase } from '~/application/contracts/usecase'
import { QueryException } from '~/domain/exceptions/query-exception'

export function createQueryFn<T, P>(service: UseCase<T, P>, params?: P) {
  return async (): Promise<T> => {
    const response = params
      ? await service.execute(params)
      : await service.execute()

    if (response.isFailure()) {
      throw new QueryException(
        response.error?.message || 'Unknown error',
        response.error
      )
    }

    return response.value as T
  }
}
