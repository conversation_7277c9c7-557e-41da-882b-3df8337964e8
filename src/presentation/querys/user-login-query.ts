import { makeEmailLoginService } from '~/application/usecases'
import { createQueryFn } from './helpers/create-query'

type Props = {
  email: string
  password: string
}

export const userLoginQueryKey = ['loadUserAuthenticated']

export const userLoginQuery = ({ email, password }: Props) => {
  const emailLoginService = makeEmailLoginService()

  return {
    queryKey: userLoginQueryKey,
    queryFn: createQueryFn(emailLoginService, { email, password })
  }
}
