import { queryOptions } from '@tanstack/react-query'
import { makeValidateSecurityTokenService } from '~/application/usecases/auth/validate-security-token'
import { createQueryFn } from './helpers/create-query'

type Props = {
  securityToken: string
}

export const validateSecurityTokenQuery = ({ securityToken }: Props) => {
  const validateSecurityTokenService = makeValidateSecurityTokenService()

  return queryOptions({
    queryKey: ['validateSecurityToken', securityToken],
    queryFn: createQueryFn(validateSecurityTokenService, { securityToken })
  })
}
