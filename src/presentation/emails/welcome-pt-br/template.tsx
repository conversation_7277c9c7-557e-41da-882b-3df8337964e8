import {
  Mjml,
  MjmlHead,
  MjmlTitle,
  MjmlPreview,
  MjmlBody,
  MjmlSection,
  MjmlColumn,
  MjmlButton,
  MjmlImage,
  MjmlText,
  MjmlRaw
} from '@faire/mjml-react'
import { renderToMjml } from '@faire/mjml-react/utils/renderToMjml'

export const WelcomeEmailMJMLPTBR = renderToMjml(
  <Mjml>
    <MjmlHead>
      <MjmlRaw>
        <link
          href='https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap'
          rel='stylesheet'
        />
      </MjmlRaw>
      <MjmlTitle>Softo - Seja bem vindo a plataforma.</MjmlTitle>
      <MjmlPreview>Agora você pode desfrutar de todas...</MjmlPreview>
    </MjmlHead>
    <MjmlBody backgroundColor='#F2F2F2'>
      <MjmlSection>
        <MjmlColumn width={552}>
          <MjmlImage
            width={116}
            height={49}
            src='https://softo-boilerplate-assets-dev.s3.amazonaws.com/images/logo.png'
            alt='Goal logo'
          />
        </MjmlColumn>
      </MjmlSection>
      <MjmlSection padding={0}>
        <MjmlColumn
          width={552}
          padding='64px 32px'
          borderRadius={16}
          backgroundColor='#FFF'
        >
          <MjmlText
            fontFamily='Verdana'
            fontSize={24}
            fontWeight='400'
            color='#191C1A'
          >
            Seja bem vindo a plataforma.
          </MjmlText>
          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            paddingTop={24}
            color='#191C1A'
          >
            Agora você pode desfrutar de todas as funcionalidades acessando com
            seu usuário e senha clicando no link abaixo.
          </MjmlText>

          <MjmlButton
            paddingTop={32}
            fontFamily='Montserrat, sans-serif'
            fontWeight='600'
            fontSize={12}
            padding='11px 24px'
            borderRadius={4}
            height={32}
            backgroundColor='#FF6600'
            href='{{linkUrl}}'
          >
            Ir para o login
          </MjmlButton>
        </MjmlColumn>
      </MjmlSection>
      <MjmlSection>
        <MjmlColumn>
          <MjmlText
            fontSize={12}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={16}
            color='#5E6170'
            align='center'
          >
            Este e-mail foi enviado para {'{{userEmail}}'}
          </MjmlText>

          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            paddingTop={24}
            color='#5E6170'
            align='center'
          >
            © 2022 Goal - Todos os direitos reservados
          </MjmlText>
        </MjmlColumn>
      </MjmlSection>
    </MjmlBody>
  </Mjml>
)
