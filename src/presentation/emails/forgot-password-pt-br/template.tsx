import {
  Mjml,
  MjmlHead,
  MjmlTitle,
  MjmlPreview,
  MjmlBody,
  MjmlSection,
  MjmlColumn,
  MjmlButton,
  MjmlImage,
  MjmlText,
  MjmlRaw
} from '@faire/mjml-react'
import { renderToMjml } from '@faire/mjml-react/utils/renderToMjml'

export const ForgotPasswordMJMLPTBR = renderToMjml(
  <Mjml>
    <MjmlHead>
      <MjmlRaw>
        <link
          href='https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap'
          rel='stylesheet'
        />
      </MjmlRaw>
      <MjmlTitle>Oi, {'{{userName}}'}</MjmlTitle>
      <MjmlPreview>Crie uma nova senha clicando no botão abaixo...</MjmlPreview>
    </MjmlHead>
    <MjmlBody backgroundColor='#F2F2F2'>
      <MjmlSection>
        <MjmlColumn width={552}>
          <MjmlImage
            width={116}
            height={49}
            src='https://softo-boilerplate-assets-dev.s3.amazonaws.com/images/logo.png'
            alt='Goal logo'
          />
        </MjmlColumn>
      </MjmlSection>
      <MjmlSection padding={0}>
        <MjmlColumn
          width={552}
          padding='64px 32px'
          borderRadius={16}
          backgroundColor='#FFF'
        >
          <MjmlText
            fontFamily='Verdana'
            fontSize={24}
            fontWeight='400'
            color='#191C1A'
          >
            Oi, {'{{userName}}'}!
          </MjmlText>
          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            paddingTop={24}
            color='#191C1A'
          >
            Crie uma nova senha clicando no botão abaixo ou no link a seguir. Se
            o botão não funcionar, clique no link abaixo:{' '}
            <a href={'{{linkUrl}}'}>link</a> . Isso faz parte do procedimento
            para criar uma nova senha.
          </MjmlText>

          <MjmlButton
            paddingTop={32}
            fontFamily='Montserrat, sans-serif'
            fontWeight='600'
            fontSize={12}
            padding='11px 24px'
            borderRadius={4}
            height={32}
            backgroundColor='#FF6600'
            href='{{linkUrl}}'
          >
            Criar nova senha
          </MjmlButton>
        </MjmlColumn>
      </MjmlSection>
      <MjmlSection>
        <MjmlColumn>
          <MjmlText
            fontSize={12}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={16}
            color='#5E6170'
            align='center'
          >
            Se você não solicitou uma nova senha, ignore este e-mail e sua senha
            permanecerá a mesma. Se você precisar de ajuda, entre em contato
            conosco por e-<NAME_EMAIL>
          </MjmlText>

          <MjmlText
            fontSize={12}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={16}
            color='#5E6170'
            align='center'
          >
            Este e-mail foi enviado para {'{{userEmail}}'}
          </MjmlText>

          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            paddingTop={24}
            color='#5E6170'
            align='center'
          >
            © 2022 Goal - Todos os direitos reservados
          </MjmlText>
        </MjmlColumn>
      </MjmlSection>
    </MjmlBody>
  </Mjml>
)
