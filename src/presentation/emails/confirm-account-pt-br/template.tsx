import {
  Mjml,
  MjmlHead,
  MjmlTitle,
  MjmlPreview,
  MjmlBody,
  MjmlSection,
  MjmlColumn,
  MjmlButton,
  MjmlImage,
  MjmlText,
  MjmlRaw
} from '@faire/mjml-react'
import { renderToMjml } from '@faire/mjml-react/utils/renderToMjml'

export const ConfirmAccountPtBRMJML = renderToMjml(
  <Mjml>
    <MjmlHead>
      <MjmlRaw>
        <link
          href='https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap'
          rel='stylesheet'
        />
      </MjmlRaw>
      <MjmlTitle>Confirmação de email - Goal</MjmlTitle>
      <MjmlPreview>
        Bem-vindo(a) ao Goal! Para completar o seu cadastro...
      </MjmlPreview>
    </MjmlHead>
    <MjmlBody backgroundColor='#E4E8ED'>
      <MjmlSection>
        <MjmlColumn width={552}>
          <MjmlImage
            width={116}
            height={49}
            src='https://softo-boilerplate-assets-dev.s3.amazonaws.com/images/logo.png'
            alt='Goal logo'
          />
        </MjmlColumn>
      </MjmlSection>
      <MjmlSection>
        <MjmlColumn
          width={552}
          padding='64px'
          borderRadius={16}
          backgroundColor='#FFF'
        >
          <MjmlText
            fontFamily='Verdana'
            fontSize={24}
            lineHeight={32}
            fontWeight='400'
            color='#191C1A'
          >
            Olá, {'{{userName}}'}
          </MjmlText>
          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            paddingTop={24}
            color='#191C1A'
          >
            Bem-vindo(a) ao Goal!
          </MjmlText>
          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            color='#191C1A'
          >
            Para completar o seu cadastro, por favor, confirme seu e-mail
            clicando no link abaixo. Esta etapa é essencial para garantir sua
            segurança e acesso completo à plataforma.
          </MjmlText>

          <MjmlButton
            paddingTop={32}
            fontFamily='Montserrat, sans-serif'
            fontWeight='600'
            fontSize={12}
            padding='11px 24px'
            borderRadius={4}
            height={32}
            width={152}
            backgroundColor='#FF6600'
            href='{{linkUrl}}'
            target='_blank'
          >
            Confirmar email
          </MjmlButton>
        </MjmlColumn>
      </MjmlSection>
      <MjmlSection>
        <MjmlColumn width={426}>
          <MjmlText
            fontSize={12}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={16}
            color='#5E6170'
            align='center'
            letterSpacing={0.25}
          >
            Se você não solicitou o cadastro na plataforma Goal, simplesmente
            ignore esse e-mail.
          </MjmlText>

          <MjmlText
            fontSize={12}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={16}
            color='#5E6170'
            align='center'
            letterSpacing={0.25}
          >
            Este e-mail foi enviado para {'{{userEmail}}'}
          </MjmlText>

          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            paddingTop={20}
            color='#707070'
            align='center'
            letterSpacing={0.25}
          >
            © 2024 Goal - Todos os direitos reservados
          </MjmlText>
        </MjmlColumn>
      </MjmlSection>
    </MjmlBody>
  </Mjml>
)
