import {
  Mjml,
  MjmlHead,
  MjmlTitle,
  MjmlPreview,
  MjmlBody,
  MjmlSection,
  MjmlColumn,
  MjmlButton,
  MjmlImage,
  MjmlText,
  MjmlRaw
} from '@faire/mjml-react'
import { renderToMjml } from '@faire/mjml-react/utils/renderToMjml'

export const ConfirmAccountUSMJML = renderToMjml(
  <Mjml>
    <MjmlHead>
      <MjmlRaw>
        <link
          href='https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap'
          rel='stylesheet'
        />
      </MjmlRaw>
      <MjmlTitle>Email Confirmation - Goal</MjmlTitle>
      <MjmlPreview>
        Welcome to Goal! To complete your registration...
      </MjmlPreview>
    </MjmlHead>
    <MjmlBody backgroundColor='#E4E8ED'>
      <MjmlSection>
        <MjmlColumn width={552}>
          <MjmlImage
            width={116}
            height={49}
            src='https://softo-boilerplate-assets-dev.s3.amazonaws.com/images/logo.png'
            alt='Goal logo'
          />
        </MjmlColumn>
      </MjmlSection>
      <MjmlSection>
        <MjmlColumn
          width={552}
          padding='64px'
          borderRadius={16}
          backgroundColor='#FFF'
        >
          <MjmlText
            fontFamily='Verdana'
            fontSize={24}
            lineHeight={32}
            fontWeight='400'
            color='#191C1A'
          >
            Hello {'{{userName}}'}
          </MjmlText>
          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            paddingTop={24}
            color='#191C1A'
          >
            Welcome to Goal!
          </MjmlText>
          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            color='#191C1A'
          >
            To complete your registration, please confirm your email address by
            clicking on the link below. This step is essential to ensure your
            safety and complete access to the platform.
          </MjmlText>

          <MjmlButton
            paddingTop={32}
            fontFamily='Montserrat, sans-serif'
            fontWeight='600'
            fontSize={12}
            padding='11px 24px'
            borderRadius={7}
            height={32}
            width={148}
            backgroundColor='#FF6600'
            href='{{linkUrl}}'
            target='_blank'
          >
            Confirm email
          </MjmlButton>
        </MjmlColumn>
      </MjmlSection>
      <MjmlSection>
        <MjmlColumn width={480}>
          <MjmlText
            fontSize={12}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={16}
            color='#5E6170'
            align='center'
            letterSpacing={0.25}
          >
            If you haven’t registered on Goal, please ignore this email.
          </MjmlText>

          <MjmlText
            fontSize={12}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={16}
            color='#5E6170'
            align='center'
            letterSpacing={0.25}
          >
            This email was sent to {'{{userEmail}}'}
          </MjmlText>

          <MjmlText
            fontSize={14}
            fontFamily='Verdana'
            fontWeight='400'
            lineHeight={20}
            paddingTop={20}
            color='#707070'
            align='center'
            letterSpacing={0.25}
          >
            © 2024 Goal - All rights reserved.
          </MjmlText>
        </MjmlColumn>
      </MjmlSection>
    </MjmlBody>
  </Mjml>
)
