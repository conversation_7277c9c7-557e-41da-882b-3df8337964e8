import { getLogger } from '~/main/factories/make-logger'
import type { AppLogger, LogLevel } from '~/domain/entities/logger'

export interface TimedOperationResult<T> {
  result: T
  duration: number
}

export interface LoggedOperationOptions {
  loggerName?: string
  level?: LogLevel
  includeArgs?: boolean
  includeResult?: boolean
}

export interface HttpResponse {
  statusCode?: number
  body?: any
  error?: any
  message?: string
}

export interface HttpErrorCategory {
  client: boolean
  server: boolean
  network: boolean
}

export interface HttpLogContext {
  operationId: string
  method: string
  url: string
  requestId: string
  [key: string]: any
}

/**
 * Times an operation and logs the result
 */
export async function timeOperation<T>(
  operationName: string,
  operation: () => Promise<T>,
  logger: AppLogger,
  context: Record<string, any> = {}
): Promise<TimedOperationResult<T>> {
  const startTime = Date.now()
  const operationId = generateOperationId()

  logger.info(`${operationName} started`, {
    operationId,
    ...context
  })

  try {
    const result = await operation()
    const duration = Date.now() - startTime

    logger.info(`${operationName} completed`, {
      operationId,
      success: true,
      performance: analyzePerformance(duration),
      ...context
    })

    return { result, duration }
  } catch (error: any) {
    const duration = Date.now() - startTime

    logger.error(`${operationName} failed`, {
      operationId,
      duration,
      error: error.message,
      errorType: error.constructor.name,
      ...context
    })

    throw error
  }
}

/**
 * Detects if an HTTP response represents an error
 */
export function isHttpError(response: HttpResponse): boolean {
  return !!(
    response.error ||
    (response.statusCode && response.statusCode >= 400)
  )
}

/**
 * Categorizes HTTP errors by type
 */
export function categorizeHttpError(response: HttpResponse): HttpErrorCategory {
  const statusCode = response.statusCode

  // Check if it's a network error by examining the error structure
  const isNetworkError = !!(
    response.error &&
    typeof response.error === 'object' &&
    response.error.type === 'NetworkError'
  )

  return {
    client:
      !isNetworkError &&
      !!(statusCode && statusCode >= 400 && statusCode < 500),
    server: !isNetworkError && !!(statusCode && statusCode >= 500),
    network: isNetworkError
  }
}

/**
 * Logs HTTP operations with proper error detection and categorization
 */
export async function logHttpOperation<T extends HttpResponse>(
  operationName: string,
  operation: () => Promise<T>,
  logger: AppLogger,
  context: HttpLogContext,
  options: {
    slowRequestThreshold?: number
  } = {}
): Promise<T> {
  const { slowRequestThreshold = 2000 } = options
  const startTime = Date.now()

  logger.info(`${operationName} started`, {
    ...context,
    operationId: context.requestId
  })

  try {
    const response = await operation()
    const duration = Date.now() - startTime
    const isError = isHttpError(response)

    if (isError) {
      logHttpError(operationName, context, response, duration, logger)
    } else {
      logHttpSuccess(
        operationName,
        context,
        response,
        duration,
        logger,
        slowRequestThreshold
      )
    }

    return response
  } catch (error: any) {
    const duration = Date.now() - startTime
    logHttpException(operationName, context, error, duration, logger)
    throw error
  }
}

/**
 * Logs successful HTTP responses
 */
export function logHttpSuccess(
  operationName: string,
  context: HttpLogContext,
  response: HttpResponse,
  duration: number,
  logger: AppLogger,
  slowRequestThreshold: number = 2000
): void {
  logger.info(`${operationName} completed`, {
    ...context,
    operationId: context.requestId,
    statusCode: response.statusCode,
    success: true,
    performance: analyzePerformance(duration, slowRequestThreshold)
  })
}

/**
 * Logs HTTP error responses (4xx, 5xx)
 */
export function logHttpError(
  operationName: string,
  context: HttpLogContext,
  response: HttpResponse,
  duration: number,
  logger: AppLogger
): void {
  const errorCategory = categorizeHttpError(response)

  const logLevel = errorCategory.client ? 'warn' : 'error'

  logger[logLevel](`${operationName} failed`, {
    ...context,
    operationId: context.requestId,
    statusCode: response.statusCode,
    success: false,
    errorMessage: response.message || 'HTTP error',
    errorType: errorCategory,
    retryable: errorCategory.server || errorCategory.network,
    performance: analyzePerformance(duration)
  })
}

/**
 * Logs HTTP exceptions (network failures, timeouts)
 */
export function logHttpException(
  operationName: string,
  context: HttpLogContext,
  error: any,
  duration: number,
  logger: AppLogger
): void {
  logger.error(`${operationName} failed with exception`, {
    ...context,
    operationId: context.requestId,
    duration,
    success: false,
    error: error.message,
    errorType: error.constructor.name,
    retryable: true,
    performance: analyzePerformance(duration)
  })
}

/**
 * Creates a higher-order function that wraps any function with logging
 */
export function withLogging<TArgs extends any[], TReturn>(
  fn: (...args: TArgs) => TReturn | Promise<TReturn>,
  operationName: string,
  options: LoggedOperationOptions = {}
): (...args: TArgs) => Promise<TReturn> {
  const logger = getLogger(options.loggerName || 'wrapped-function')
  const level = options.level || 'INFO'

  const logMessage = (message: string, context: Record<string, any>) => {
    switch (level) {
      case 'FATAL':
        logger.fatal(message, context)
        break
      case 'ERROR':
        logger.error(message, context)
        break
      case 'WARN':
        logger.warn(message, context)
        break
      case 'INFO':
        logger.info(message, context)
        break
      case 'DEBUG':
        logger.debug(message, context)
        break
      case 'TRACE':
        logger.trace(message, context)
        break
      default:
        logger.info(message, context)
    }
  }

  return async (...args: TArgs): Promise<TReturn> => {
    const operationId = generateOperationId()
    const startTime = Date.now()

    const context: Record<string, any> = {
      operationId,
      operationName
    }

    if (options.includeArgs) {
      context.arguments = args
    }

    logMessage(`${operationName} started`, context)

    try {
      const result = await fn(...args)
      const duration = Date.now() - startTime

      const successContext: Record<string, any> = {
        operationId,
        operationName,
        duration,
        success: true
      }

      if (options.includeResult) {
        successContext.result = result
      }

      logMessage(`${operationName} completed`, successContext)

      return result
    } catch (error: any) {
      const duration = Date.now() - startTime

      logger.error(`${operationName} failed`, {
        operationId,
        operationName,
        duration,
        error: error.message,
        errorType: error.constructor.name
      })

      throw error
    }
  }
}

/**
 * Creates structured context for consistent logging
 */
export function createLogContext(
  component: string,
  action: string,
  additionalContext: Record<string, any> = {}
): Record<string, any> {
  return {
    component,
    action,
    timestamp: new Date().toISOString(),
    ...additionalContext
  }
}

/**
 * Logs user actions with consistent structure
 */
export function logUserAction(
  logger: AppLogger,
  action: string,
  userId?: string,
  context: Record<string, any> = {}
): void {
  logger.info('User action performed', {
    action,
    userId,
    userAction: true,
    ...context
  })
}

/**
 * Logs business events with consistent structure
 */
export function logBusinessEvent(
  logger: AppLogger,
  eventName: string,
  eventData: Record<string, any> = {}
): void {
  logger.info('Business event occurred', {
    eventName,
    eventType: 'business',
    ...eventData
  })
}

/**
 * Logs performance metrics
 */
export function logPerformanceMetric(
  logger: AppLogger,
  metricName: string,
  value: number,
  unit: string = 'ms',
  context: Record<string, any> = {}
): void {
  logger.info('Performance metric recorded', {
    metricName,
    value,
    unit,
    performance: analyzePerformance(value),
    ...context
  })
}

/**
 * Creates a simple wrapper for React component logging
 */
export function withComponentLogging<TProps extends Record<string, any>>(
  componentName: string,
  loggerName?: string
) {
  const logger = getLogger(
    loggerName || `component-${componentName.toLowerCase()}`
  )

  logger.setGlobalContext({
    component: 'react-component',
    componentName
  })

  return {
    logger,
    logRender: (props: TProps) => {
      logger.debug('Component rendered', {
        action: 'render',
        propsKeys: Object.keys(props)
      })
    },
    logEffect: (effectName: string, dependencies?: string[]) => {
      logger.debug('Effect executed', {
        action: 'effect',
        effectName,
        dependencies
      })
    },
    logEvent: (eventName: string, eventData?: Record<string, any>) => {
      logger.info('Component event', {
        action: 'event',
        eventName,
        ...eventData
      })
    }
  }
}

/**
 * Generates a unique operation ID
 */
export function generateOperationId(): string {
  return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Analyzes operation performance
 */
export function analyzePerformance(
  duration: number,
  slowThreshold: number = 1000
) {
  return {
    duration,
    fast: duration < 100,
    slow: duration > slowThreshold,
    category:
      duration < 100 ? 'fast' : duration < slowThreshold ? 'normal' : 'slow'
  }
}

/**
 * Creates data size string representation
 */
export function getDataSize(data: any): string {
  if (!data) return '0B'

  try {
    const sizeInBytes =
      typeof data === 'string' ? data.length : JSON.stringify(data).length

    if (sizeInBytes < 1024) return `${sizeInBytes}B`
    if (sizeInBytes < 1024 * 1024) return `${(sizeInBytes / 1024).toFixed(1)}KB`
    return `${(sizeInBytes / (1024 * 1024)).toFixed(1)}MB`
  } catch {
    return 'unknown'
  }
}
