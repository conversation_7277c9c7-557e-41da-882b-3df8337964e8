export const validatePassword = ({ password }: { password: string }) => {
  const hasUpperCase = password?.toLowerCase() !== password
  const hasLowerCase = password?.toUpperCase() !== password
  const hasSpecialCharacter = /[ !"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]/.test(
    password ?? ''
  )
  const hasNumber = /\d/.test(password ?? '')
  const hasMinLength = password ? password?.length >= 8 : false

  const isValid =
    hasUpperCase &&
    hasLowerCase &&
    hasSpecialCharacter &&
    hasNumber &&
    hasMinLength

  return { isValid }
}
