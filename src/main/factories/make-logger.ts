import { ConsoleLogger } from '~/infra/logger/console-logger'
import { DatadogLogger } from '~/infra/logger/datadog-logger'
import { AppLogger, type Logger } from '~/domain/entities/logger'
import {
  NODE_ENV,
  DATADOG_CLIENT_TOKEN,
  DATADOG_SITE,
  DATADOG_SERVICE,
  DATADOG_ENV,
  PROJECT_NAME,
  PROJECT_VERSION
} from '~/main/env'

class LoggerFactory {
  private static initialized = false
  private static loggerInstances = new Map<string, AppLogger>()

  static initialize(): void {
    if (this.initialized) {
      console.warn(
        'LoggerFactory is already initialized. Skipping re-initialization.'
      )
      return
    }

    this.initializeDatadog()
    this.initialized = true
  }

  static createLogger(serviceName: string = 'app'): AppLogger {
    if (!this.loggerInstances.has(serviceName)) {
      const loggerImpl = this.createLoggerImplementation(serviceName)
      const appLogger = new AppLogger(loggerImpl, serviceName)
      this.loggerInstances.set(serviceName, appLogger)
    }

    return this.loggerInstances.get(serviceName)!
  }

  static getLogger(serviceName: string = 'app'): AppLogger {
    return this.createLogger(serviceName)
  }

  static getActiveLoggers(): Map<string, AppLogger> {
    return new Map(this.loggerInstances)
  }

  static clearCache(): void {
    this.loggerInstances.clear()
  }

  private static initializeDatadog(): void {
    if (NODE_ENV === 'production' && DATADOG_CLIENT_TOKEN) {
      try {
        DatadogLogger.init({
          clientToken: DATADOG_CLIENT_TOKEN,
          site: DATADOG_SITE,
          service: DATADOG_SERVICE || PROJECT_NAME,
          env: DATADOG_ENV || NODE_ENV,
          version: PROJECT_VERSION
        })
      } catch (error) {
        console.error('Failed to initialize Datadog logger:', error)
      }
    }
  }

  private static createLoggerImplementation(serviceName: string): Logger {
    if (NODE_ENV === 'production' && DATADOG_CLIENT_TOKEN) {
      return new DatadogLogger(serviceName)
    }
    return new ConsoleLogger(serviceName)
  }
}

/**
 * Initializes the logging system. Should be called once during application startup.
 * Configures Datadog in production environment if credentials are available.
 */
export function initializeLogger(): void {
  LoggerFactory.initialize()
}

/**
 * Creates or retrieves a cached logger instance for the specified service.
 * @param serviceName - The name of the service/component requesting the logger
 * @returns AppLogger instance configured for the service
 */
export function getLogger(serviceName: string = 'app'): AppLogger {
  return LoggerFactory.getLogger(serviceName)
}

/**
 * Gets all currently active logger instances.
 * Useful for debugging and monitoring.
 * @returns Map of service names to their logger instances
 */
export function getActiveLoggers(): Map<string, AppLogger> {
  return LoggerFactory.getActiveLoggers()
}

/**
 * Clears all cached logger instances.
 * Useful for testing or when you need to reset the logging state.
 */
export function clearLoggerCache(): void {
  LoggerFactory.clearCache()
}
