import { getLogger } from './make-logger'

const logger = getLogger('global-error-handler')

export function initializeGlobalErrorHandler(): void {
  logger.setGlobalContext({
    component: 'global-error-handler',
    service: 'frontend-monitoring'
  })

  window.addEventListener('error', (event) => {
    const errorContext = {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      errorType: 'javascript-error'
    }

    logger.fatal('Unhandled JavaScript error', errorContext)
  })

  window.addEventListener('unhandledrejection', (event) => {
    const errorContext = {
      reason: event.reason,
      stack: event.reason?.stack,
      message: event.reason?.message || String(event.reason),
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      errorType: 'unhandled-promise-rejection'
    }

    logger.fatal('Unhandled promise rejection', errorContext)
  })

  logger.info('Global error handler initialized', {
    handlers: ['error', 'unhandledrejection'],
    timestamp: new Date().toISOString()
  })
}
