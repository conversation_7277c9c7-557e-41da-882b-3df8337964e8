import z from 'zod'

const envSchema = z.object({
  API_URL: z.string(),
  UNDER_MAINTENANCE: z.string().transform((value) => value === 'true'),
  NODE_ENV: z.enum(['development', 'production', 'test', 'staging']),
  BUCKET_URL: z.string(),
  DISABLE_VERSION_API: z.string().transform((value) => value === 'true'),
  DISABLE_VERSION_FRONT: z.string().transform((value) => value === 'true'),
  OTEL_EXPORTER_OTLP_ENDPOINT: z.string().optional(),
  PROJECT_NAME: z.string(),
  PROJECT_VERSION: z.string(),
  DATADOG_CLIENT_TOKEN: z.string().optional(),
  DATADOG_SITE: z.string().optional(),
  DATADOG_SERVICE: z.string().optional(),
  DATADOG_ENV: z.string().optional()
})

export const {
  API_URL,
  UNDER_MAINTENANCE,
  NODE_ENV,
  BUCKET_URL,
  DISABLE_VERSION_API,
  DISABLE_VERSION_FRONT,
  OTEL_EXPORTER_OTLP_ENDPOINT,
  PROJECT_NAME,
  PROJECT_VERSION,
  DATADOG_CLIENT_TOKEN,
  DATADOG_SITE,
  DATADOG_SERVICE,
  DATADOG_ENV
} = envSchema.parse({
  API_URL: import.meta.env.VITE_API_URL,
  UNDER_MAINTENANCE: import.meta.env.VITE_UNDER_MAINTENANCE,
  NODE_ENV: import.meta.env.VITE_NODE_ENV,
  BUCKET_URL: import.meta.env.VITE_BUCKET_URL,
  DISABLE_VERSION_API: import.meta.env.VITE_DISABLE_VERSION_API,
  DISABLE_VERSION_FRONT: import.meta.env.VITE_DISABLE_VERSION_FRONT,
  OTEL_EXPORTER_OTLP_ENDPOINT: import.meta.env.VITE_OTEL_EXPORTER_OTLP_ENDPOINT,
  PROJECT_NAME: import.meta.env.VITE_PROJECT_NAME,
  PROJECT_VERSION: import.meta.env.VITE_PROJECT_VERSION,
  DATADOG_CLIENT_TOKEN: import.meta.env.VITE_DATADOG_CLIENT_TOKEN,
  DATADOG_SITE: import.meta.env.VITE_DATADOG_SITE,
  DATADOG_SERVICE: import.meta.env.VITE_DATADOG_SERVICE,
  DATADOG_ENV: import.meta.env.VITE_DATADOG_ENV
})
