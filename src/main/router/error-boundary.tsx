import { Button } from 'softo-design-system'
import { useNavigate } from 'react-router'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { ROUTES } from '../types'

export const ErrorBoundary = () => {
  const navigate = useNavigate()
  const { translate } = useTranslation()

  return (
    <div className='flex flex-col items-center justify-center w-full h-full bg-white'>
      <h1 className='mt-4 text-[57px] font-bold text-center uppercase max-w-[620px]'>
        {translate('error_title')}
      </h1>
      <span className='block my-4 text-center text-base mb-12'>
        {translate('error_description')}
      </span>

      <Button className='uppercase' onClick={() => navigate(ROUTES.HOME)}>
        {translate('error_btn_return')}
      </Button>
    </div>
  )
}
