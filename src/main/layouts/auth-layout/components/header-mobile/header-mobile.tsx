import { BadgeEnvironment } from '~/presentation/components/badge-environment/badge-environment'
import LogoGoalMobile from '/assets/images/logo-goal-mobile.svg'
import { Image } from '~/presentation/components/image/image'

export const HeaderMobile = () => {
  return (
    <header className='h-[88px] bg-palettes-primary-10 flex items-center justify-center py-4 gap-3'>
      <Image
        layout='constrained'
        width={120}
        height={40}
        src={LogoGoalMobile}
        alt='Logo Goal Mobile'
      />
      <BadgeEnvironment
        variant='outline'
        className='text-white border border-white bg-transparent'
      />
    </header>
  )
}
