import { Outlet, useLocation } from 'react-router'
import PictureSoftoLogin from '/assets/images/outside-background.jpeg'
import LogoGoal from '/assets/images/goal-logo.svg'
import { DropdownLanguage } from '~/presentation/components/dropdown-language/dropdown-language'
import { useTranslation } from '~/presentation/hooks/use-translation'
import { ROUTES } from '~/main/types'
import { cn } from '~/presentation/utils/cn'
import { HeaderMobile } from './components/header-mobile/header-mobile'
import { useEffect, useState } from 'react'
import { useMediaQuery } from '~/presentation/hooks/use-media-query'
import { Image } from '~/presentation/components/image/image'

const AuthLayout = () => {
  const { translate } = useTranslation('common')
  const location = useLocation()
  const isMobile = useMediaQuery('(max-width:640px)')
  const [animate, setAnimate] = useState(false)

  useEffect(() => {
    setAnimate(false)
    const timer = setTimeout(() => setAnimate(true), 0) // Trigger reflow to restart the animation
    return () => clearTimeout(timer) // Clean up the timer on unmount or rerender
  }, [location])

  const isRegisterPage = location.pathname.includes(ROUTES.REGISTER)
  const isLoginPage = location.pathname.includes(ROUTES.LOGIN)

  const enableAnimation = animate && isLoginPage && isMobile

  return (
    <div className='relative flex h-full flex-col md:flex-row'>
      <div className={cn(isRegisterPage ? 'block md:hidden' : 'hidden')}>
        <HeaderMobile />
      </div>

      <div
        className={cn(
          'w-full h-[45%] md:h-full',
          isRegisterPage ? 'hidden md:flex' : ''
        )}
      >
        <Image
          src={PictureSoftoLogin}
          layout='fullWidth'
          className='w-full h-full object-cover'
          alt='Goal Sidebar'
        />
      </div>

      <div
        className={cn(
          'overflow-auto w-full h-full rounded-3xl md:rounded-none md:border-0 md:-mt-0 bg-white pt-8 px-6 md:px-6',
          isRegisterPage ? '-mt-0' : '-mt-6',
          enableAnimation && 'animate-slideup'
        )}
      >
        <div className='md:flex gap-4 justify-end items-center w-full mb-14 hidden'>
          <span>{translate('label_select_language')}</span>
          <DropdownLanguage />
        </div>

        <div className={cn(isRegisterPage ? 'hidden md:block' : '')}>
          <Image
            src={LogoGoal}
            layout='constrained'
            width={isMobile ? 120 : 175}
            height={isMobile ? 45 : 65}
            className='mx-auto mb-8 md:mb-28'
            alt='Logo Goal'
          />
        </div>

        <Outlet />
      </div>
    </div>
  )
}

export default AuthLayout
