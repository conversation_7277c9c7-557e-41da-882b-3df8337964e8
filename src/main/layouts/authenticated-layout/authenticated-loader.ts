import type { QueryClient } from '@tanstack/react-query'
import { redirect } from 'react-router'

import { ROUTES } from '~/main/types'
import { loadAccountMeQuery } from '~/presentation/querys/load-account-me-query'

export const authenticatedLoader = (queryClient: QueryClient) => async () => {
  const userAuthenticated = await queryClient.ensureQueryData(
    loadAccountMeQuery()
  )

  const url = new URL(window.location.href)
  const previousUrl = `?redirect=${encodeURIComponent(url.pathname)}`

  if (!userAuthenticated) return redirect(`${ROUTES.LOGIN}${previousUrl}`)

  return null
}
