import { useState } from 'react'
import { Navigate, Outlet } from 'react-router'

import { UNDER_MAINTENANCE } from '~/main/env'
import { ROUTES } from '~/main/types'
import { Header, Menu } from '~/presentation/components'
import { cn } from '~/presentation/utils/cn'

const AuthenticatedLayout = () => {
  const [isOpen, toggleMenu] = useState(false)

  const onToggleMenu = () => toggleMenu((prev) => !prev)

  if (UNDER_MAINTENANCE)
    return <Navigate to={ROUTES.UNDER_MAINTENANCE} replace />

  return (
    <div className='flex w-full h-full'>
      <div className='hidden md:block'>
        <Menu toggleMenu={onToggleMenu} isOpen={isOpen} />
      </div>

      <div
        className={cn('flex w-full flex-col', {
          'md:w-[calc(100vw-64px)]': !isOpen,
          'md:w-[calc(100vw-240px)]': isOpen
        })}
      >
        <Header />
        <main>
          <Outlet />
        </main>
      </div>
    </div>
  )
}

export default AuthenticatedLayout
