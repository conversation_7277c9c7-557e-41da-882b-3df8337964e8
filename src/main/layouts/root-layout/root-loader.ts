import { type LoaderFunctionArgs, redirect } from 'react-router'

import { UNDER_MAINTENANCE } from '~/main/env'
import { ROUTES, ROUTES_EMAIL } from '~/main/types'

import type { QueryClient } from '@tanstack/react-query'
import { loadTenantQuery } from '../auth-layout/querys/load-tenant-query'
import { loadAccountMeQuery } from '~/presentation/querys/load-account-me-query'

export const rootLoader =
  (queryClient: QueryClient) =>
  async ({ request }: LoaderFunctionArgs) => {
    if (UNDER_MAINTENANCE) return redirect(ROUTES.UNDER_MAINTENANCE)

    const url = new URL(request.url)

    const isWhiteListUrl = [
      ROUTES.LOGIN,
      ROUTES.FORGOT_PASSWORD,
      ROUTES.REGISTER,
      ROUTES.CONFIRM_EMAIL,
      ROUTES.RESET_PASSWORD,
      ROUTES_EMAIL.WELCOME_PT_BR,
      ROUTES_EMAIL.WELCOME_US,
      ROUTES_EMAIL.FORGOT_PASSWORD_PT_BR,
      ROUTES_EMAIL.FORGOT_PASSWORD_US,
      ROUTES_EMAIL.CONFIRM_ACCOUNT_PT_BR,
      ROUTES_EMAIL.CONFIRM_ACCOUNT_US
    ].includes(url.pathname as ROUTES)

    const previousUrl = `?redirect=${encodeURIComponent(url.pathname)}`

    try {
      await queryClient.ensureQueryData(loadTenantQuery())
      await queryClient.ensureQueryData(loadAccountMeQuery())
      if (isWhiteListUrl) return redirect(ROUTES.HOME)
      return null
    } catch {
      if (isWhiteListUrl) return null
      return redirect(`${ROUTES.LOGIN}${previousUrl}`)
    }
  }
