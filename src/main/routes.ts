// import { lazy, Suspense } from "react";
// import { type RouteObject } from "react-router";
// import { NODE_ENV } from "~/main/env";
// import { authenticatedLoader } from "~/main/layouts/authenticated-layout/authenticated-loader";
// import { MaintenanceLayout } from "~/main/layouts/maintenance-layout/maintenance-layout";
// import { rootLoader } from "~/main/layouts/root-layout/root-loader";
// import { ErrorBoundary } from "~/main/router/error-boundary";
// import { ROUTES, ROUTES_EMAIL } from "~/main/types";
// import { confirmEmailLoader } from "~/presentation/pages/confirm-email/confirm-email-loader";
// import { forgotPasswordAction } from "~/presentation/pages/forgot-password/forgot-password-action";
// import { loginAction } from "~/presentation/pages/login/login-action";
// import { registerAction } from "~/presentation/pages/register/register-action";
// import { resetPasswordAction } from "~/presentation/pages/reset-password/reset-password-action";
// import { resetPasswordLoader } from "~/presentation/pages/reset-password/reset-password-loader";
// import ConfirmEmail from "~/presentation/pages/confirm-email/confirm-email";
// import ConfirmEmailError from "~/presentation/pages/confirm-email/confirm-email-error";
// import ResetPasswordError from "~/presentation/pages/reset-password/reset-password-error";
// import { permissionsProfileDetailsLoader } from "~/presentation/pages/permissions-profile-details/permissions-profile-details-loader";

// const isDevelopment = NODE_ENV === "development";

// const AuthLayout = lazy(
//   async () => import("~/main/layouts/auth-layout/auth-layout")
// );

// const RootLayout = lazy(
//   async () => import("~/main/layouts/root-layout/root-layout")
// );

// const Login = lazy(async () => import("~/presentation/pages/login/login"));

// const ForgotPassword = lazy(
//   async () => import("~/presentation/pages/forgot-password/forgot-password")
// );

// const Register = lazy(
//   async () => import("~/presentation/pages/register/register")
// );

// const ResetPassword = lazy(
//   async () => import("~/presentation/pages/reset-password/reset-password")
// );

// const UnderMaintenance = lazy(
//   async () => import("~/presentation/pages/under-maintenance/under-maintenance")
// );

// const Home = lazy(async () => import("~/presentation/pages/home/<USER>"));
// const PermissionsProfile = lazy(
//   async () =>
//     import("~/presentation/pages/permissions-profile/permissions-profile")
// );
// const PermissionsProfileDetails = lazy(
//   async () =>
//     import(
//       "~/presentation/pages/permissions-profile-details/permissions-profile-details"
//     )
// );

// const AuthenticatedLayout = lazy(
//   async () => import("~/main/layouts/authenticated-layout/authenticated-layout")
// );

// const ForgotPasswordEmailPtBR = lazy(
//   () => import("~/presentation/emails/forgot-password-pt-br")
// );
// const WelcomeEmailPtBR = lazy(
//   () => import("~/presentation/emails/welcome-pt-br")
// );
// const ConfirmAccountEmailPtBR = lazy(
//   () => import("~/presentation/emails/confirm-account-pt-br")
// );

// const ForgotPasswordEmailUS = lazy(
//   () => import("~/presentation/emails/forgot-password-us")
// );
// const WelcomeEmailUS = lazy(() => import("~/presentation/emails/welcome-us"));
// const ConfirmAccountEmailUS = lazy(
//   () => import("~/presentation/emails/confirm-account-us")
// );

import { index, layout, route } from "@react-router/dev/routes";

export default [
  index("../presentation/pages/home/<USER>"),
  layout("../main/layouts/auth-layout/auth-layout.tsx", [
    route("login", "../presentation/pages/login/login.tsx"),
  ]),
];

// export const appRoutes: RouteObject[] = [
//   {
//     element: (
//       <Suspense>
//         <RootLayout />
//       </Suspense>
//     ),
//     loader: rootLoader(queryClient),
//     errorElement: <ErrorBoundary />,
//     children: [
//       {
//         element: (
//           <Suspense>
//             <MaintenanceLayout />
//           </Suspense>
//         ),
//         children: [
//           { path: ROUTES.UNDER_MAINTENANCE, element: <UnderMaintenance /> }
//         ]
//       },
//       {
//         element: (
//           <Suspense>
//             <AuthLayout />
//           </Suspense>
//         ),
//         children: [
//           {
//             path: ROUTES.LOGIN,
//             element: (
//               <Suspense>
//                 <Login />
//               </Suspense>
//             ),
//             action: loginAction(queryClient)
//           },
//           {
//             path: ROUTES.FORGOT_PASSWORD,
//             element: (
//               <Suspense>
//                 <ForgotPassword />
//               </Suspense>
//             ),
//             action: forgotPasswordAction
//           },
//           {
//             path: ROUTES.REGISTER,
//             element: (
//               <Suspense>
//                 <Register />
//               </Suspense>
//             ),
//             action: registerAction
//           },
//           {
//             path: ROUTES.CONFIRM_EMAIL,
//             element: (
//               <Suspense>
//                 <ConfirmEmail />
//               </Suspense>
//             ),
//             loader: confirmEmailLoader(queryClient),
//             errorElement: <ConfirmEmailError />
//           },
//           {
//             path: ROUTES.RESET_PASSWORD,
//             element: (
//               <Suspense>
//                 <ResetPassword />
//               </Suspense>
//             ),
//             errorElement: <ResetPasswordError />,
//             loader: resetPasswordLoader(queryClient),
//             action: resetPasswordAction
//           }
//         ]
//       },
//       {
//         element: (
//           <Suspense>
//             <AuthenticatedLayout />
//           </Suspense>
//         ),
//         loader: authenticatedLoader(queryClient),
//         shouldRevalidate: () => true,
//         children: [
//           {
//             path: ROUTES.HOME,
//             element: (
//               <Suspense>
//                 <Home />
//               </Suspense>
//             )
//           },
//           {
//             path: ROUTES.PERMISSIONS_PROFILE,
//             element: (
//               <Suspense>
//                 <PermissionsProfile />
//               </Suspense>
//             )
//           },
//           {
//             path: ROUTES.PERMISSIONS_PROFILE_DETAILS,
//             element: (
//               <Suspense>
//                 <PermissionsProfileDetails />
//               </Suspense>
//             ),
//             loader: permissionsProfileDetailsLoader(queryClient),
//             errorElement: <ErrorBoundary />
//           }
//         ]
//       },
//       ...(isDevelopment
//         ? [
//             {
//               path: ROUTES_EMAIL.WELCOME_PT_BR,
//               element: (
//                 <Suspense>
//                   <WelcomeEmailPtBR />
//                 </Suspense>
//               )
//             },
//             {
//               path: ROUTES_EMAIL.WELCOME_US,
//               element: (
//                 <Suspense>
//                   <WelcomeEmailUS />
//                 </Suspense>
//               )
//             },
//             {
//               path: ROUTES_EMAIL.FORGOT_PASSWORD_PT_BR,
//               element: (
//                 <Suspense>
//                   <ForgotPasswordEmailPtBR />
//                 </Suspense>
//               )
//             },
//             {
//               path: ROUTES_EMAIL.FORGOT_PASSWORD_US,
//               element: (
//                 <Suspense>
//                   <ForgotPasswordEmailUS />
//                 </Suspense>
//               )
//             },
//             {
//               path: ROUTES_EMAIL.CONFIRM_ACCOUNT_PT_BR,
//               element: (
//                 <Suspense>
//                   <ConfirmAccountEmailPtBR />
//                 </Suspense>
//               )
//             },
//             {
//               path: ROUTES_EMAIL.CONFIRM_ACCOUNT_US,
//               element: (
//                 <Suspense>
//                   <ConfirmAccountEmailUS />
//                 </Suspense>
//               )
//             }
//           ]
//         : [])
//     ]
//   }
// ]
