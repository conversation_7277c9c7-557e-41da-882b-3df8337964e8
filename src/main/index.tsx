import { StrictMode, Suspense, lazy } from 'react'
import { createRoot } from 'react-dom/client'
import {
  NODE_ENV,
  OTEL_EXPORTER_OTLP_ENDPOINT,
  PROJECT_NAME,
  PROJECT_VERSION
} from '~/main/env'
import { initTracer } from '~/infra/logger/open-telemetry-init-tracer'
import { initializeLogger } from '~/main/factories/make-logger'
import { initializeGlobalErrorHandler } from '~/main/factories/global-error-handler'

import '~/main/config/i18n/i18next-setup'
import 'material-icons/iconfont/material-icons.css'
import './index.css'

initTracer(PROJECT_NAME, {
  debug: NODE_ENV === 'development',
  environment: NODE_ENV,
  endpoint: OTEL_EXPORTER_OTLP_ENDPOINT,
  serviceVersion: PROJECT_VERSION,
  serviceName: PROJECT_NAME
})

initializeLogger()
initializeGlobalErrorHandler()

const container = document.getElementById('root')!

const root = createRoot(container)

async function enableMocking() {
  if (NODE_ENV !== 'test') return

  const { worker } = await import('./config/tests/mocks/browser')

  return await worker.start({ onUnhandledRequest: 'bypass' })
}

enableMocking().then(async () => {
  const App = lazy(async () => import('./app'))
  root.render(
    <StrictMode>
      <Suspense>
        <App />
      </Suspense>
    </StrictMode>
  )
})
