import { Account<PERSON>eHand<PERSON> } from './handlers/auth/account-me'
import { <PERSON><PERSON><PERSON>andler } from './handlers/auth/login'
import { ValidateSecurityTokenHandler } from './handlers/auth/validate-security-token'
import { ResendAccountConfirmationHandler } from './handlers/auth/resend-account-confirmation'
import { ConfirmAccountHandler } from './handlers/auth/confirm-account'
import { RegisterUserHandler } from './handlers/auth/register-user'
import { ProfileHandler } from './handlers/auth/profile'
import { GetSystemConfigHandler } from './handlers/general/get-system-config'
import { LogoutHandler } from './handlers/auth/logout'
import { TenantRegister } from './handlers/auth/tenant-register'
import { ForgotPasswordHandler } from './handlers/auth/forgot-password'
import { ResetPasswordHandler } from './handlers/auth/reset-password'
import { AddRoleUserHandler } from './handlers/roles/add-role-user'
import { <PERSON>ad<PERSON>olesHandler } from './handlers/roles/load-roles'
import { CreateRoleUserHandler } from './handlers/roles/create-role'
import { DeleteRoleUserHandler } from './handlers/roles/delete-role-user'
import { LoadRoleHandler } from './handlers/roles/load-role'
import { LoadRoleUsersHandler } from './handlers/roles/load-role-users'
import { LoadRoleUsersLinkedHandler } from './handlers/roles/load-role-users-linked'
import { LoadRoleUsersUnLinkedHandler } from './handlers/roles/load-role-users-unlinked'
import { LoadRolesPermissionsHandler } from './handlers/roles/load-roles-permissions'
import { UpdatePermissionHandler } from './handlers/roles/update-permission'
import { UpdateRoleHandler } from './handlers/roles/update-role'
import { LoadTenantHandler } from './handlers/general/load-tenant'

export const handlers = [
  ...LoginHandler,
  ...ForgotPasswordHandler,
  ...TenantRegister,
  ...LogoutHandler,
  ...GetSystemConfigHandler,
  ...ProfileHandler,
  ...RegisterUserHandler,
  ...ConfirmAccountHandler,
  ...ResendAccountConfirmationHandler,
  ...ValidateSecurityTokenHandler,
  ...AccountMeHandler,
  ...ResetPasswordHandler,
  ...AddRoleUserHandler,
  ...LoadRolesHandler,
  ...CreateRoleUserHandler,
  ...DeleteRoleUserHandler,
  ...LoadRoleHandler,
  ...LoadRoleUsersHandler,
  ...LoadRoleUsersLinkedHandler,
  ...LoadRoleUsersUnLinkedHandler,
  ...LoadRolesPermissionsHandler,
  ...UpdatePermissionHandler,
  ...UpdateRoleHandler,
  ...LoadTenantHandler
]
