import { HttpResponse, http } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { LoadTenant } from '~/application/usecases/tenant/load-tenant'

export const LoadTenantHandler = [
  http.get(getApiRoute('tenant')().url, async () => {
    return HttpResponse.json<LoadTenant.Response>(
      {
        id: 'cb2ba53a-021a-424a-b94b-42a5cbca57d2',
        name: 'softo',
        logoUrl: '',
        url: 'http://localhost:3000',
        slug: 'softo-slug',
        designTokens: {}
      } as LoadTenant.Response,
      { status: 201 }
    )
  })
]
