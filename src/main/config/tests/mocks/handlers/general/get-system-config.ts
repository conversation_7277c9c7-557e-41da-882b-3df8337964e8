import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { GetSystemConfig } from '~/application/usecases'

export const GetSystemConfigHandler = [
  http.get(getApiRoute('system-config')().url, async () => {
    return HttpResponse.json<GetSystemConfig.Response>(
      {
        version: '1.0.0',
        updatedDate: 1627890123456,
        environment: 'development'
      },
      { status: 201 }
    )
  })
]
