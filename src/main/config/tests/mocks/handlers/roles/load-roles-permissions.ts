import { HttpResponse, http } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { LoadRoleUsersLinked } from '~/application/usecases/roles/load-role-users-linked'
import type { LoadRolesPermissions } from '~/application/usecases/roles/load-roles-permissions'

export const LoadRolesPermissionsHandler = [
  http.get(
    getApiRoute('load-roles')({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
      language: 'en-US'
    }).url,
    async ({ request }) => {
      const isKeywordRequestEmpty = request.url.includes('not_found_permission')

      if (
        request.url.includes('9a3e8688-7c1e-4a2d-afd3-c2ffb4113333') &&
        !isKeywordRequestEmpty
      ) {
        return HttpResponse.json<LoadRolesPermissions.Response>(
          [
            {
              id: '29455e54-cdf5-4720-8494-b4046806b053',
              name: 'Access',
              type: 'group',
              children: [
                {
                  id: '0ce28ad7-2a22-43cd-8b16-d75b5764660d',
                  name: 'Permission profile',
                  type: 'group',
                  children: [
                    {
                      id: '1fdab142-50b4-4b8f-938e-97c02afd24ec',
                      name: 'Create permission profile',
                      action: 'Create',
                      type: 'permission',
                      isActive: true
                    }
                  ]
                }
              ]
            }
          ] as LoadRolesPermissions.Response,
          { status: 201 }
        )
      }

      if (isKeywordRequestEmpty) {
        return HttpResponse.json<LoadRolesPermissions.Response>([])
      }
    }
  )
]
