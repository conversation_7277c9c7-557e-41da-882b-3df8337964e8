import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { UpdateRole } from '~/application/usecases/roles/update-role'

export const UpdateRoleHandler = [
  http.patch(
    getApiRoute('update-role')({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333'
    }).url,
    async ({ request }) => {
      const payload = (await request.json()) as UpdateRole.Params

      if (
        request.url.includes('9a3e8688-7c1e-4a2d-afd3-c2ffb4113333') &&
        payload.name === 'John Due'
      ) {
        return HttpResponse.json(
          {
            success: true,
            message: 'Successfully update role'
          },
          { status: 201 }
        )
      }

      if (payload.name === 'invalid_name') {
        return HttpResponse.json(
          {
            success: false,
            message: 'Invalid data'
          },
          { status: 400 }
        )
      }
    }
  )
]
