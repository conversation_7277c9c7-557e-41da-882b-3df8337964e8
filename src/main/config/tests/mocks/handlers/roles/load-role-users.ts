import { HttpResponse, http } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { LoadRoleUsers } from '~/application/usecases/roles/load-role-users'

export const LoadRoleUsersHandler = [
  http.get(
    getApiRoute('load-role-users')({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333'
    }).url,
    async ({ request }) => {
      if (request.url.includes('9a3e8688-7c1e-4a2d-afd3-c2ffb4113333')) {
        return HttpResponse.json<LoadRoleUsers.Response>(
          {
            rows: [
              {
                id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4115555',
                name: '<PERSON>',
                email: '<EMAIL>',
                status: 'USER_ACTIVE'
              },
              {
                id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4115554',
                name: '<PERSON>',
                email: '<EMAIL>',
                status: 'USER_ACTIVE'
              }
            ],
            count: 10,
            page: 1,
            pageSize: 10,
            pageCount: 10,
            pageNumberIsGood: true,
            hasPreviousPage: false,
            hasNextPage: true,
            isFirstPage: true,
            isLastPage: false,
            numberOfFirstItemOnPage: 0,
            firstItemOnPage: 0,
            numberOfLastItemOnPage: 10,
            lastItemOnPage: 10
          } as LoadRoleUsers.Response,
          { status: 201 }
        )
      }
    }
  )
]
