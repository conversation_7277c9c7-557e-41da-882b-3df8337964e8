import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'

export const CreateRoleUserHandler = [
  http.post(getApiRoute('create-role')().url, async ({ request }) => {
    const payload = (await request.json()) as {
      name: string
    }

    if (payload.name === 'Fake Role') {
      return HttpResponse.json(
        {
          success: true,
          message: 'Successfully create role'
        },
        { status: 201 }
      )
    }

    if (payload.name === 'invalid_role') {
      return HttpResponse.json(
        {
          success: false,
          message: 'Invalid data'
        },
        { status: 400 }
      )
    }
  })
]
