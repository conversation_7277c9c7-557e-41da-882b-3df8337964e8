import { HttpResponse, http } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { LoadRoles } from '~/application/usecases/roles/load-roles'

export const LoadRolesHandler = [
  http.get(getApiRoute('role')().url, async () => {
    return HttpResponse.json<LoadRoles.Response>(
      {
        rows: [
          {
            id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4117714',
            name: 'Admin default',
            isUserDefault: false,
            isAdminDefault: true
          }
        ],
        count: 60,
        page: 1,
        pageSize: 10,
        pageCount: 6,
        pageNumberIsGood: true,
        hasPreviousPage: false,
        hasNextPage: true,
        isFirstPage: true,
        isLastPage: false,
        numberOfFirstItemOnPage: 0,
        firstItemOnPage: 0,
        numberOfLastItemOnPage: 9,
        lastItemOnPage: 59
      } as LoadRoles.Response,
      { status: 201 }
    )
  })
]
