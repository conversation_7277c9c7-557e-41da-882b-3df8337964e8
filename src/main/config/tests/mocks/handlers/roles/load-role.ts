import { HttpResponse, http } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { LoadRole } from '~/application/usecases/roles/load-role'

export const LoadRoleHandler = [
  http.get(
    getApiRoute('load-role')({ roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4117777' })
      .url,
    async ({ request }) => {
      if (request.url.includes('9a3e8688-7c1e-4a2d-afd3-c2ffb4117777')) {
        return HttpResponse.json<LoadRole.Response>(
          {
            id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333',
            isAdminDefault: true,
            isUserDefault: true,
            name: '<PERSON>'
          } as LoadRole.Response,
          { status: 201 }
        )
      }
    }
  )
]
