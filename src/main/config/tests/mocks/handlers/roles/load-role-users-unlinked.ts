import { HttpResponse, http } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { LoadRoleUsersLinked } from '~/application/usecases/roles/load-role-users-linked'

export const LoadRoleUsersUnLinkedHandler = [
  http.get(
    getApiRoute('load-role-users-unlinked')({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333'
    }).url,
    async ({ request }) => {
      if (
        request.url.includes('9a3e8688-7c1e-4a2d-afd3-c2ffb4113333') &&
        request.url.includes('Due')
      ) {
        return HttpResponse.json<LoadRoleUsersLinked.Response>(
          [
            {
              id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4115555',
              name: '<PERSON>',
              email: '<EMAIL>',
              status: 'USER_ACTIVE'
            },
            {
              id: '9a3e8688-7c1e-4a2d-afd3-c2ffb4115554',
              name: '<PERSON>',
              email: '<EMAIL>',
              status: 'USER_ACTIVE'
            }
          ],
          { status: 201 }
        )
      }

      if (request.url.includes('not_found')) {
        return HttpResponse.json<LoadRoleUsersLinked.Response>([])
      }
    }
  )
]
