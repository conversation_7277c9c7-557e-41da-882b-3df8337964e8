import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'

export const DeleteRoleUserHandler = [
  http.delete(
    getApiRoute('delete-role-user')({
      roleId: '8e0a1e73-8454-475c-b8ce-3d063d944cf6',
      userId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4117777'
    }).url,
    async ({ request }) => {
      if (
        request.url.includes('8e0a1e73-8454-475c-b8ce-3d063d944cf6') &&
        request.url.includes('9a3e8688-7c1e-4a2d-afd3-c2ffb4117777')
      ) {
        return HttpResponse.json(
          {
            success: true,
            message: 'Successfully delete user'
          },
          { status: 201 }
        )
      }
    }
  )
]
