import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'

export const AddRoleUserHandler = [
  http.post(
    getApiRoute('add-role-user')({
      roleId: '999e8888-7c1e-4a2d-afd3-c2ffb4117777'
    }).url,
    async ({ request }) => {
      const payload = (await request.json()) as {
        roleId: string
        id: string
      }

      if (payload.id === '9a3e8688-7c1e-4a2d-afd3-c2ffb4117777') {
        return HttpResponse.json(
          {
            success: true,
            message: 'Successfully associated user'
          },
          { status: 201 }
        )
      }

      if (payload.id === '999e8888-7c1e-4a2d-afd3-c2ffb4117777') {
        return HttpResponse.json(
          {
            success: false,
            message: 'Not found'
          },
          { status: 404 }
        )
      }
    }
  )
]
