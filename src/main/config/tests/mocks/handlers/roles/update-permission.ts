import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { UpdatePermission } from '~/application/usecases/roles/update-permission'

export const UpdatePermissionHandler = [
  http.patch(
    getApiRoute('role-update')({
      roleId: '9a3e8688-7c1e-4a2d-afd3-c2ffb4113333'
    }).url,
    async ({ request }) => {
      const payload = (await request.json()) as UpdatePermission.Params

      if (
        request.url.includes('9a3e8688-7c1e-4a2d-afd3-c2ffb4113333') &&
        payload.id === '9a3e8688-7c1e-4a2d-afd3-c2ffb4114444'
      ) {
        return HttpResponse.json(
          {
            success: true,
            message: 'Successfully update role'
          },
          { status: 201 }
        )
      }

      if (payload.id === '9a3e8688-7c1e-4a2d-afd3-c2ffb4114445') {
        return HttpResponse.json(
          {
            success: false,
            message: 'Invalid data'
          },
          { status: 404 }
        )
      }
    }
  )
]
