import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'

export const LogoutHandler = [
  http.post(getApiRoute('account/logout')().url, async ({ cookies }) => {
    if (
      cookies.accessToken === 'invalid_access_token' &&
      cookies.refreshToken === 'invalid_refresh_token'
    ) {
      return HttpResponse.json(
        {
          success: false,
          message: 'UnAuthorized'
        },
        { status: 401 }
      )
    }

    return HttpResponse.json(
      {
        success: true,
        message: 'Successfully Logout'
      },
      { status: 201 }
    )
  })
]
