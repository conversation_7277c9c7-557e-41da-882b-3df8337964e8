import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { GetUserProfile } from '~/application/usecases'

export const ProfileHandler = [
  http.get(
    getApiRoute<{ id: string }>('profile')({ id: ':id' }).url,
    async ({ params }) => {
      if (params?.id === '7d330a09-c159-48b4-917f-d2d5dba6f063') {
        return HttpResponse.json<GetUserProfile.Response>(
          {
            id: '7d330a09-c159-48b4-917f-d2d5dba6f063',
            email: '<EMAIL>',
            username: '<PERSON>',
            avatar: 'avatar',
            isAdmin: true
          } as GetUserProfile.Response,
          { status: 201 }
        )
      }

      return HttpResponse.json(
        {
          success: false,
          message: 'User profile not found'
        },
        { status: 404 }
      )
    }
  )
]
