import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'

export const ConfirmAccountHandler = [
  http.post(
    getApiRoute('account/confirm-account')().url,
    async ({ request }) => {
      const payload = (await request.json()) as {
        securityToken: string
        tenantId: string
      }

      if (payload?.securityToken === 'faker_valid_security_token') {
        return HttpResponse.json(
          {
            success: true,
            message: 'Successfully confirm account'
          },
          { status: 201 }
        )
      }

      return HttpResponse.json(
        {
          success: false,
          message: 'Invalid security token'
        },
        { status: 400 }
      )
    }
  )
]
