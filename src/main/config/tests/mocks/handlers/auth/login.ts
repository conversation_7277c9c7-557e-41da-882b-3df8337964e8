import { http, HttpResponse } from 'msw'
import { z } from 'zod'
import { getApiRoute } from '~/application/api/api-routes'

type LoginResponse = {
  success?: boolean
  accessToken?: string
  refreshToken?: string
  message?: string
}

export const LoginHandler = [
  http.post(getApiRoute('account/login')().url, async ({ request }) => {
    const payload = (await request.json()) as {
      email: string
      password: string
    }

    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/

    const validationSchema = z.object({
      email: z.string().min(1).max(100).email(),
      password: z.string().min(8).regex(passwordRegex)
    })

    const validationResult = validationSchema.safeParse(payload)

    if (validationResult.success) {
      return HttpResponse.json<LoginResponse>(
        {
          accessToken: 'valid-token',
          refreshToken: 'valid-refresh-token'
        },
        {
          status: 201,
          headers: {
            'Set-Cookie': 'token=valid-token;'
          }
        }
      )
    }

    return HttpResponse.json(
      {
        success: false,
        message: 'Invalid credentials'
      },
      { status: 403 }
    )
  })
]
