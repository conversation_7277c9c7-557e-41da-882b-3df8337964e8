import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'

export const ResetPasswordHandler = [
  http.post(
    getApiRoute('account/reset-password')().url,
    async ({ request }) => {
      const payload = (await request.json()) as {
        password: string
        securityToken: string
      }

      if (
        payload.password === '123456' &&
        payload.securityToken === 'invalid_token'
      ) {
        return HttpResponse.json(
          {
            success: false,
            message: 'Invalid Token'
          },
          { status: 401 }
        )
      }

      return HttpResponse.json(
        {
          success: true,
          message: 'Successfully Reset Password'
        },
        { status: 201 }
      )
    }
  )
]
