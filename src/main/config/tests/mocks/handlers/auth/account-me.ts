import { HttpResponse, http } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'
import type { AccountMe } from '~/application/usecases/auth/account-me'

export const AccountMeHandler = [
  http.get(getApiRoute('account/me')().url, async ({ cookies }) => {
    if (
      cookies.accessToken === 'invalid_access_token' &&
      cookies.refreshToken === 'invalid_refresh_token'
    ) {
      return HttpResponse.json(
        {
          success: false,
          message: 'UnAuthorized'
        },
        { status: 401 }
      )
    }

    return HttpResponse.json<AccountMe.Response>(
      {
        email: '<EMAIL>',
        firstName: 'john',
        lastName: 'due',
        id: '7d330a09-c159-48b4-917f-d2d5dba6f063'
      } as AccountMe.Response,
      { status: 201 }
    )
  })
]
