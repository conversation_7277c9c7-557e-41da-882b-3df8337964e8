import { http, HttpResponse } from 'msw'
import { z } from 'zod'
import { getApiRoute } from '~/application/api/api-routes'

export const RegisterUserHandler = [
  http.post(getApiRoute('register-user')().url, async ({ request }) => {
    const payload = (await request.json()) as {
      username: string
      email: string
      password: string
    }

    const validationSchema = z.object({
      email: z.string().email(),
      password: z
        .string()
        .min(6)
        .regex(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,}$/),
      username: z.string().min(1)
    })

    const validationResult = validationSchema.safeParse(payload)

    if (validationResult.success) {
      return HttpResponse.json(
        {
          success: true,
          message: 'Successfully registered user'
        },
        { status: 201 }
      )
    }

    if (validationResult.error) {
      return HttpResponse.json(
        {
          success: false,
          message: 'Invalid input data'
        },
        { status: 400 }
      )
    }
  })
]
