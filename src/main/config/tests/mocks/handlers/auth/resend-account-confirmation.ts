import { http, HttpResponse } from 'msw'
import { z } from 'zod'
import { getApiRoute } from '~/application/api/api-routes'

export const ResendAccountConfirmationHandler = [
  http.post(
    getApiRoute('account/resend-account-confirmation')().url,
    async ({ request }) => {
      const payload = (await request.json()) as {
        email: string
      }

      const validationSchema = z.object({
        email: z.string().min(1).max(100).email()
      })

      const validationResult = validationSchema.safeParse(payload)

      if (validationResult.success) {
        return HttpResponse.json(
          {
            success: true,
            message: 'Successfully resend account confirmation'
          },
          { status: 201 }
        )
      }

      return HttpResponse.json(
        {
          success: false
        },
        { status: 400 }
      )
    }
  )
]
