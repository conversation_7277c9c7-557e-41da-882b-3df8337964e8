import { http, HttpResponse } from 'msw'
import { z } from 'zod'
import { getApiRoute } from '~/application/api/api-routes'
import type { TenantRegisterModel } from '~/domain/models/tenant-register-model'

export const TenantRegister = [
  http.post(getApiRoute('tenant/register')().url, async ({ request }) => {
    const payload = (await request.json()) as TenantRegisterModel

    const validationSchema = z.object({
      tenant: z.object({
        name: z
          .string()
          .min(1)
          .max(100)
          .regex(/^[^\s*$]/),
        slug: z.string().min(1).max(100)
      }),
      admin: z.object({
        firstName: z
          .string()
          .min(1)
          .max(100)
          .regex(/^[^\s*$]/),
        lastName: z
          .string()
          .min(1)
          .max(100)
          .regex(/^[^\s*$]/),
        email: z.string().min(1).max(100).email(),
        password: z.string().min(8)
      })
    })

    const validationResult = validationSchema.safeParse(payload)

    if (validationResult.success) {
      return HttpResponse.json(
        {
          success: true,
          message: 'Successfully registered user'
        },
        { status: 201 }
      )
    }

    if (validationResult.error) {
      return HttpResponse.json(
        {
          success: false,
          message: 'Invalid input data'
        },
        { status: 400 }
      )
    }
  })
]
