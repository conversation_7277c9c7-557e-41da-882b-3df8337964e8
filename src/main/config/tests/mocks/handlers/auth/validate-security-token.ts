import { http, HttpResponse } from 'msw'
import { getApiRoute } from '~/application/api/api-routes'

export const ValidateSecurityTokenHandler = [
  http.post(
    getApiRoute('account/validate-security-token')().url,
    async ({ request }) => {
      const payload = (await request.json()) as {
        securityToken: string
      }

      if (payload.securityToken === 'valid_security_token') {
        return HttpResponse.json(
          {
            success: true,
            message: 'SecurityToken is valid'
          },
          { status: 201 }
        )
      }

      if (payload.securityToken === 'expired_security_token') {
        return HttpResponse.json(
          {
            errors: 'Token is expired'
          },
          { status: 401 }
        )
      }

      return HttpResponse.json(
        {
          success: false
        },
        { status: 400 }
      )
    }
  )
]
