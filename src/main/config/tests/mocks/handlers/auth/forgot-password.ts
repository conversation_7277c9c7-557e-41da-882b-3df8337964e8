import { http, HttpResponse } from 'msw'
import { z } from 'zod'
import { getApiRoute } from '~/application/api/api-routes'

export const ForgotPasswordHandler = [
  http.post(
    getApiRoute('account/forgot-password')().url,
    async ({ request }) => {
      const payload = (await request.json()) as {
        email: string
      }

      const validationSchema = z.object({
        email: z.string().min(1).max(100).email()
      })

      const validationResult = validationSchema.safeParse(payload)

      if (validationResult.success) {
        return HttpResponse.json(
          {
            success: true,
            message: 'New password sent, check your email.'
          },
          { status: 201 }
        )
      }

      return HttpResponse.json(
        {
          success: false,
          message: 'Email not found.'
        },
        { status: 404 }
      )
    }
  )
]
