import React from "react";
import ReactDOM, { createRoot } from "react-dom/client";
import { HydratedRouter } from "react-router/dom";
import { initTracer } from "~/infra/logger/open-telemetry-init-tracer";
import {
  NODE_ENV,
  OTEL_EXPORTER_OTLP_ENDPOINT,
  PROJECT_NAME,
  PROJECT_VERSION,
} from "~/main/env";
import { initializeGlobalErrorHandler } from "~/main/factories/global-error-handler";
import { initializeLogger } from "~/main/factories/make-logger";

initTracer(PROJECT_NAME, {
  debug: NODE_ENV === "development",
  environment: NODE_ENV,
  endpoint: OTEL_EXPORTER_OTLP_ENDPOINT,
  serviceVersion: PROJECT_VERSION,
  serviceName: PROJECT_NAME,
});

initializeLogger();
initializeGlobalErrorHandler();

async function enableMocking() {
  if (NODE_ENV !== "test") return;

  const { worker } = await import("./config/tests/mocks/browser");

  return await worker.start({ onUnhandledRequest: "bypass" });
}

enableMocking().then(() => {
  ReactDOM.hydrateRoot(
    document,
    <React.StrictMode>
      <HydratedRouter />
    </React.StrictMode>
  );
});
