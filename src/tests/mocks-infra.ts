import { type HttpMethod, HttpStatusCode } from '~/infra/http/http-client'

export const mockHttpClientSpy = (
  response: {
    statusCode: number
  } = {
    statusCode: HttpStatusCode.OK
  }
) => {
  const httpClientSpy = {
    request: vi.fn()
  }
  httpClientSpy.request.mockResolvedValue(response)
  return httpClientSpy
}

export const mockCacheStorageSpy = (getResponse?: Record<string, any>) => {
  const cacheStorageSpy = {
    set: vi.fn(),
    get: vi.fn()
  }
  if (getResponse) {
    cacheStorageSpy.get.mockReturnValue(getResponse)
  }
  return cacheStorageSpy
}

export const mockRoute = (url: string, method: HttpMethod) => {
  return () => {
    return {
      url,
      method
    }
  }
}
