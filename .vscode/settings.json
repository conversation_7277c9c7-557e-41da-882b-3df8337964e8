{"editor.bracketPairColorization.independentColorPoolPerBracketType": true, "editor.tabSize": 2, "editor.insertSpaces": false, "editor.codeActionsOnSave": {"source.fixAll": "always"}, "editor.definitionLinkOpensInPeek": true, "[sass]": {"editor.tabSize": 2}, "editor.formatOnSave": true, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[html]": {"editor.defaultFormatter": "biomejs.biome"}, "[yaml]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}}