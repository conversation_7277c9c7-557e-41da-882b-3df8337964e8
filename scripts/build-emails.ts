import mjml2html from 'mjml'
import { mkdirSync, writeFileSync } from 'node:fs'
import path, { join, relative } from 'node:path'
import { rmSync } from 'node:fs'
import pretty from 'pretty'

// import all templates
import { templates } from '../src/presentation/emails/templates'
import { fileURLToPath } from 'node:url'

// Convert import.meta.url to __dirname
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const DIST_DIR = join(__dirname, '..', 'emails')
const MJML_DIR = join(DIST_DIR, 'mjml')
const HTML_DIR = join(DIST_DIR, 'html')
const TEMPLATES: { [key: string]: string } = templates

// Function to format MJML with new lines for each tag
const formatMJML = (mjml: string) => {
  return mjml
    .replace(/>\s*</g, '>\n<')
    .replace(/(<[^\/>]+>)([^<]+)/g, '$1\n  $2\n')
    .replace(/\n\s*\n/g, '\n')
}

// ensure dist dir before outputing our built templates
const ensureDirSync = (dir: string) => {
  try {
    mkdirSync(dir, { recursive: true })
  } catch (error) {
    if (
      error instanceof Error &&
      (error as { code?: string }).code === 'EEXIST'
    ) {
      rmSync(dir, { recursive: true })
      mkdirSync(dir, { recursive: true })
    } else {
      throw error
    }
  }
}

ensureDirSync(DIST_DIR)
ensureDirSync(MJML_DIR)
ensureDirSync(HTML_DIR)

for (const name of Object.keys(TEMPLATES)) {
  const TemplateMJML = TEMPLATES[name]
  const { html } = mjml2html(TemplateMJML, { validationLevel: 'strict' })
  const formattedMJML = pretty(formatMJML(TemplateMJML), { ocd: true })

  const mjmlPath = join(MJML_DIR, `${name}.mjml`)
  writeFileSync(mjmlPath, formattedMJML, { encoding: 'utf-8' })
  console.debug(`Wrote ${relative(__dirname, mjmlPath)}`)

  const htmlPath = join(HTML_DIR, `${name}.html`)
  writeFileSync(htmlPath, html, { encoding: 'utf-8' })
  console.debug(`Wrote ${relative(__dirname, htmlPath)}`)
}
