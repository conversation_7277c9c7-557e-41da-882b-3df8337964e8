name: ci

on:
  pull_request:
    branches: [develop]
    types: [opened, synchronize, closed]
  push:
    branches:
      - develop

jobs:
  build:
    runs-on: ubuntu-22.04

    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      VITE_API_URL: http://localhost:3333
      VITE_UNDER_MAINTENANCE: false
      VITE_BUCKET_URL: 'https://softo-boilerplate-assets-dev.s3.amazonaws.com'
      VITE_NODE_ENV: 'production'
      VITE_DISABLE_VERSION_API: false
      VITE_DISABLE_VERSION_FRONT: false

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3.3.0
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - run: npm install -g pnpm
      - run: pnpm install
      - run: pnpm test --verbose
      - run: pnpm check-types
      - run: pnpm lint --fix
      - run: pnpm build
  release:
    name: Release
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Cache Node modules
        uses: actions/cache@v3
        with:
          path: |
            ~/.npm
            node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: lts/*
      - name: Install dependencies
        run: npm install
      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: HUSKY=0 npx semantic-release