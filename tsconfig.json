{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "verbatimModuleSyntax": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ES2020", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "rootDir": ".", "baseUrl": ".", "types": ["vite/client", "node", "vitest/globals"], "paths": {"~/*": ["./src/*"]}}, "exclude": ["node_modules"]}