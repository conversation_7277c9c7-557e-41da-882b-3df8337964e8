{"$schema": "https://biomejs.dev/schemas/1.8.0/schema.json", "organizeImports": {"enabled": true}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "linter": {"enabled": true, "rules": {"recommended": false, "a11y": {"noBlankTarget": "error"}, "nursery": {"useSortedClasses": "off"}, "complexity": {"noBannedTypes": "off", "noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noStaticOnlyClass": "off", "noUselessCatch": "error", "noUselessConstructor": "error", "noUselessLoneBlockStatements": "error", "noUselessRename": "error", "noUselessTernary": "error", "noUselessTypeConstraint": "error", "noVoid": "error", "noWith": "error", "useLiteralKeys": "error", "useOptionalChain": "error", "useRegexLiterals": "error"}, "correctness": {"noChildrenProp": "error", "noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidConstructorSuper": "error", "noInvalidUseBeforeDeclaration": "off", "noNewSymbol": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "off", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnusedVariables": "error", "useArrayLiterals": "off", "useExhaustiveDependencies": "off", "useHookAtTopLevel": "error", "useIsNan": "error", "useJsxKeyInIterable": "error"}, "security": {"noDangerouslySetInnerHtmlWithChildren": "error", "noGlobalEval": "error"}, "style": {"noCommaOperator": "error", "noNamespace": "off", "noNonNullAssertion": "off", "noVar": "warn", "useBlockStatements": "off", "useConsistentArrayType": {"level": "error", "options": {"syntax": "shorthand"}}, "useConst": "error", "useShorthandFunctionType": "error", "useSingleVarDeclarator": "error"}, "suspicious": {"noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCommentText": "error", "noCompareNegZero": "error", "noConfusingVoidType": "off", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDoubleEquals": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateJsxProps": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "off", "noSelfCompare": "error", "noShadowRestrictedNames": "error", "noUnsafeNegation": "error", "useDefaultSwitchClauseLast": "error", "useValidTypeof": "error"}}, "ignore": ["scripts/build-emails.ts", "node_modules", "public", "dist", "**/.vscode", "**/coverage", "**/*.json"]}, "javascript": {"formatter": {"trailingCommas": "none", "semicolons": "asNeeded", "quoteStyle": "single", "jsxQuoteStyle": "single"}, "globals": ["document", "navigator", "window"]}}