# Softo Frontend Boilerplate

A modern React.js boilerplate designed for scalable web applications, built with TypeScript and following clean architecture principles.

## Features

- **Modern Stack**: React 18, TypeScript, Vite
- **Authentication**: Complete auth flow with JWT, password reset, and email confirmation
- **Role Management**: User roles and permissions system
- **Internationalization**: Multi-language support (EN/PT-BR)
- **Clean Architecture**: Domain-driven design with clear separation of concerns
- **Developer Experience**: Biome, Lefthook, automated testing
- **Type Safety**: Full TypeScript implementation with strict typing

## Quick Start

### Prerequisites

- Node.js 18+
- pnpm (recommended package manager)

### Installation

```bash
# Clone the repository
git clone <repository-url>

# Install dependencies
pnpm install

# Start development server
pnpm dev
```

### Environment Setup

1. Copy `.env.example` to `.env`
2. Configure your environment variables
3. Update API endpoints as needed

## Project Structure

```
src/
├── application/     # Business logic and use cases
├── domain/          # Core business entities and rules
├── infra/           # External integrations and adapters
├── main/            # App configuration and dependency injection
└── presentation/    # UI components and pages
```

## Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm test` - Run tests
- `pnpm lint` - Run linter
- `pnpm check-types` - TypeScript type checking

## Technology Stack

- **Frontend**: React, TypeScript, Vite
- **Styling**: Tailwind CSS, Sass
- **State Management**: TanStack Query (React Query)
- **Testing**: Vitest, React Testing Library, MSW
- **Linting/Formatting**: Biome
- **Git Hooks**: Lefthook
- **Package Manager**: pnpm

## Documentation

Detailed documentation and guides will be available in the `docs/` folder as the project evolves.

## Contributing

Please follow the established patterns and ensure all tests pass before submitting pull requests.
