{"name": "softo-new-react-boilerplate", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -p tsconfig.prod.json && vite build", "build:emails": "tsx scripts/build-emails.ts", "update-ds-libs": "pnpm update '@softo/design-system*' --latest", "preview": "vite preview --host 0.0.0.0", "test": "vitest", "test:watch": "pnpm run test --watch", "test:changed": "pnpm run test --only<PERSON><PERSON>ed", "test:ci": "pnpm test --coverage", "lint": "biome lint", "lint:format": "biome format --write", "lint:fix": "biome lint --fix --skip-errors", "check-types": "tsc", "pre-commit": "pnpm run check-types && pnpm run test --watch=false", "prepare": "lefthook install"}, "dependencies": {"@datadog/browser-logs": "^6.18.1", "@hookform/resolvers": "^3.9.1", "@opentelemetry/api": "1.9.0", "@opentelemetry/context-zone": "2.0.1", "@opentelemetry/core": "2.0.1", "@opentelemetry/exporter-trace-otlp-http": "0.203.0", "@opentelemetry/instrumentation": "0.203.0", "@opentelemetry/instrumentation-document-load": "0.48.0", "@opentelemetry/instrumentation-fetch": "0.203.0", "@opentelemetry/instrumentation-user-interaction": "0.48.0", "@opentelemetry/instrumentation-xml-http-request": "0.203.0", "@opentelemetry/resources": "2.0.1", "@opentelemetry/sdk-trace-base": "2.0.1", "@opentelemetry/sdk-trace-web": "2.0.1", "@opentelemetry/semantic-conventions": "1.36.0", "@radix-ui/react-icons": "^1.3.2", "@react-router/node": "^7.8.2", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "5.85.5", "@unpic/react": "^0.1.14", "axios": "^1.11.0", "clsx": "^2.1.1", "i18next": "^23.16.4", "i18next-http-backend": "^2.6.2", "isbot": "^5", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "material-icons": "^1.13.14", "pretty": "^2.0.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-i18next": "^15.7.2", "react-router": "^7.8.2", "softo-design-system": "^0.0.59", "tailwind-merge": "^3.3.1", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "2.2.2", "@evilmartians/lefthook": "^1.12.3", "@faire/mjml-react": "^3.5.2", "@ngneat/falso": "^8.0.2", "@react-router/dev": "^7.8.2", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.4", "@semantic-release/npm": "^12.0.2", "@semantic-release/release-notes-generator": "^14.0.3", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/vite": "^4.1.12", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/mjml": "^4.7.4", "@types/node": "^24.3.0", "@types/pretty": "^2.0.3", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.1", "autoprefixer": "^10.4.21", "identity-obj-proxy": "^3.0.0", "jsdom": "^26.1.0", "mjml": "^4.15.3", "mjml-browser": "^4.15.3", "msw": "^2.10.5", "postcss": "^8.5.6", "semantic-release": "^24.2.7", "tailwindcss": "^4.1.12", "tsx": "^4.20.5", "typescript": "^5.9.2", "vite": "^7.1.3", "vitest": "^3.2.4"}, "msw": {"workerDirectory": ["public"]}, "release": {"branches": [{"name": "develop"}], "repositoryUrl": "https://github.com/SoftoDev/Softo-Frontend-Web-ReactJs-New-Boilerplate", "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/npm", {"npmPublish": false}], ["@semantic-release/git", {"message": "chore(release): ${nextRelease.version} \n\n${nextRelease.notes}"}], "@semantic-release/github"]}, "packageManager": "pnpm@9.9.0+sha512.60c18acd138bff695d339be6ad13f7e936eea6745660d4cc4a776d5247c540d0edee1a563695c183a66eb917ef88f2b4feb1fc25f32a7adcadc7aaf3438e99c1"}