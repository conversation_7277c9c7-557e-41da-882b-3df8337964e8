# Logger System Documentation

## 📋 Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [Architecture](#architecture)
- [Development vs Production](#development-vs-production)
- [Log Levels Guide](#log-levels-guide)
- [HTTP Logging](#http-logging)
- [Creating Loggers](#creating-loggers)
- [Logging Utilities](#logging-utilities)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)
- [For Non-Technical Stakeholders](#for-non-technical-stakeholders)

---

## Overview

Our logging system provides **structured, contextual logging** for the entire application with:

- 🏭 **Centralized Factory Pattern** - One place to create all loggers
- 🎯 **Service-Specific Context** - Each service gets its own logger with relevant context
- 🌍 **Environment-Aware** - Different behavior in development vs production
- 📊 **Rich HTTP Logging** - Automatic request/response logging with timing and error categorization
- 🔧 **Reusable Utilities** - Common logging patterns made simple
- 📈 **Performance Monitoring** - Automatic timing and performance categorization
- 🔍 **Error Categorization** - Smart detection of client, server, and network errors

---

## Quick Start

### 1. Initialize the Logger System

In your main application entry point (`src/main/index.tsx`):

```typescript
import { initializeLogger } from "~/main/factories/make-logger";

// Initialize once at startup
initializeLogger();
```

### 2. Create a Logger for Your Service

```typescript
import { getLogger } from "~/main/factories/make-logger";

const logger = getLogger("your-service-name");

// Set context that will be included in all logs from this service
logger.setGlobalContext({
  service: "user-management",
  version: "2.1.0",
});
```

### 3. Start Logging

```typescript
// Basic logging
logger.info("User login attempt", { userId: "123", email: "<EMAIL>" });
logger.error("Login failed", { userId: "123", reason: "invalid-credentials" });

// HTTP client with automatic logging
import { makeHttpClient } from "~/infra/http";
const httpClient = makeHttpClient("auth-service"); // Gets automatic HTTP logging
```

---

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │    │   HTTP Client   │    │   React Component│
│    Services     │    │   (with logs)   │    │   (with logs)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                   ┌─────────────────────────┐
                   │    LoggerFactory        │
                   │   (Creates Loggers)     │
                   └─────────────────────────┘
                                 │
                   ┌─────────────────────────┐
                   │      AppLogger          │
                   │  (Adds metadata &       │
                   │   context management)   │
                   └─────────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ ConsoleLogger   │    │  DatadogLogger  │    │  Future Logger  │
│ (Development)   │    │  (Production)   │    │ (Custom/Other)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## Development vs Production

### 🛠️ Development Mode

**What You See:**

- **All log levels** displayed in browser console
- **Formatted output** with colors and readable structure
- **Detailed error information** including stack traces
- **Console-friendly format** for easy debugging

**Example Output:**

```
[2024-01-15T10:30:00Z] [auth-service] INFO: User login attempt {userId: "123", email: "<EMAIL>"}
[2024-01-15T10:30:01Z] [http-client] ERROR: POST /api/login failed {statusCode: 401, duration: 234ms}
```

**Configuration:**

```typescript
// Automatically uses ConsoleLogger
// Shows: FATAL, ERROR, WARN, INFO, DEBUG, TRACE
```

### 🚀 Production Mode

**What Happens:**

- **Logs sent to Datadog** (if configured) for centralized monitoring
- **Only critical logs** (FATAL, ERROR, WARN) are sent to reduce noise
- **Structured JSON format** for better parsing and alerting
- **Automatic batching** and network optimization

**Example Datadog Log:**

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "ERROR",
  "service": "auth-service",
  "message": "User login failed",
  "context": {
    "userId": "123",
    "errorType": "invalid-credentials",
    "duration": 234,
    "statusCode": 401
  }
}
```

**Configuration:**

```typescript
// Uses DatadogLogger when DATADOG_CLIENT_TOKEN is provided
// Shows only: FATAL, ERROR, WARN
```

---

## Log Levels Guide

### When to Use Each Level

| Level        | When to Use                         | Examples                                      | Production? |
| ------------ | ----------------------------------- | --------------------------------------------- | ----------- |
| 🔥 **FATAL** | Application cannot continue         | Database unreachable, critical config missing | ✅ Yes      |
| 🚨 **ERROR** | Something broke but app continues   | API call failed, user operation failed        | ✅ Yes      |
| ⚠️ **WARN**  | Something unexpected but not broken | Deprecated API used, fallback activated       | ✅ Yes      |
| ℹ️ **INFO**  | Normal operations, business events  | User logged in, payment processed             | ❌ Dev only |
| 🔍 **DEBUG** | Detailed execution flow             | Function entry/exit, variable values          | ❌ Dev only |
| 📝 **TRACE** | Very detailed execution             | Loop iterations, detailed state               | ❌ Dev only |

### Examples

```typescript
// ❌ FATAL - Application cannot continue
logger.fatal("Database connection failed", {
  error: error.message,
  connectionString: "postgresql://...",
  retryAttempts: 5,
});

// 🚨 ERROR - Operation failed
logger.error("Payment processing failed", {
  userId: "123",
  amount: 99.99,
  paymentMethod: "card",
  errorCode: "INSUFFICIENT_FUNDS",
});

// ⚠️ WARN - Something concerning but not broken
logger.warn("API response slower than expected", {
  endpoint: "/api/users",
  duration: 3500,
  threshold: 2000,
});

// ℹ️ INFO - Normal business operations
logger.info("User profile updated", {
  userId: "123",
  fieldsChanged: ["email", "phone"],
  source: "user-settings-page",
});

// 🔍 DEBUG - Development debugging
logger.debug("Cache hit for user data", {
  userId: "123",
  cacheKey: "user:123:profile",
  ttl: 3600,
});

// 📝 TRACE - Very detailed debugging
logger.trace("Processing user permissions", {
  userId: "123",
  roles: ["admin", "user"],
  permissions: ["read", "write", "delete"],
});
```

---

## HTTP Logging

### Automatic HTTP Logging

All HTTP clients automatically log:

- 📤 **Request initiation** with method, URL, and context
- ⏱️ **Response completion** with status, timing, and performance analysis
- 🚨 **Error categorization** (client, server, network errors)
- 🎯 **Retry recommendations** based on error type

### Using Logged HTTP Clients

```typescript
import { makeHttpClient, makeTenantHttpClient } from "~/infra/http";

// Basic HTTP client with logging
const httpClient = makeHttpClient("payment-service");

// Tenant-aware HTTP client with logging
const tenantClient = makeTenantHttpClient("user-service");

// Custom HTTP client with advanced options
import { makeCustomHttpClient } from "~/infra/http";
const customClient = makeCustomHttpClient({
  loggerName: "external-api",
  logRequestBodies: true, // Log request payload sizes
  logResponseBodies: false, // Don't log response payloads
  slowRequestThreshold: 1500, // Requests > 1.5s are "slow"
});
```

### HTTP Error Types

```typescript
// 🌐 Network Error (ERR_CONNECTION_REFUSED, timeout)
{
  "errorType": { "client": false, "server": false, "network": true },
  "retryable": true,
  "logLevel": "ERROR"
}

// 🔴 Server Error (5xx responses)
{
  "errorType": { "client": false, "server": true, "network": false },
  "retryable": true,
  "logLevel": "ERROR"
}

// ⚠️ Client Error (4xx responses)
{
  "errorType": { "client": true, "server": false, "network": false },
  "retryable": false,
  "logLevel": "WARN"
}
```

---

## Creating Loggers

### Basic Logger Creation

```typescript
// Create a basic logger
const logger = getLogger("service-name");

// Set persistent context (included in all logs)
logger.setGlobalContext({
  service: "authentication",
  version: "2.1.0",
  environment: "production",
});
```

### Service-Specific Examples

```typescript
// Authentication Service
const authLogger = getLogger("auth-service");
authLogger.setGlobalContext({
  component: "authentication",
  version: "2.1.0",
});

// Database Service
const dbLogger = getLogger("database-service");
dbLogger.setGlobalContext({
  component: "database",
  connectionPool: "primary",
});

// React Component
const profileLogger = getLogger("user-profile-component");
profileLogger.setGlobalContext({
  component: "react-component",
  route: "/profile",
});
```

### Performance Tips

- **✅ Reuse loggers** - The factory caches instances by name
- **✅ Set context once** - Use `setGlobalContext()` in constructor/initialization
- **❌ Don't create new loggers** in frequently called functions

---

## Logging Utilities

### Function Wrapping with Automatic Logging

```typescript
import { withLogging } from "~/presentation/utils/logging-utils";

// Wrap any function with automatic timing and error logging
const processPayment = withLogging(
  async (amount: number, userId: string) => {
    // Your business logic here
    return await paymentService.charge(amount, userId);
  },
  "process-payment",
  {
    loggerName: "payment-service",
    includeArgs: false, // Don't log sensitive payment data
  }
);

// Usage - automatically logs start, completion/error, and timing
const result = await processPayment(99.99, "user123");
```

### Timing Operations

```typescript
import { timeOperation } from "~/presentation/utils/logging-utils";

const logger = getLogger("data-processing");

const { result, duration } = await timeOperation(
  "process-large-dataset",
  async () => {
    return await processData(userData);
  },
  logger,
  {
    userId: user.id,
    dataSize: "1.2MB",
  }
);

console.log(`Processing took ${duration}ms`);
```

### React Component Logging

```typescript
import { withComponentLogging } from "~/presentation/utils/logging-utils";

function UserProfile({ userId }: { userId: string }) {
  const { logger, logRender, logEffect, logEvent } =
    withComponentLogging("UserProfile");

  // Log renders (DEBUG level - development only)
  logRender({ userId });

  useEffect(() => {
    logEffect("fetch-user-data", ["userId"]);
    fetchUserData(userId);
  }, [userId]);

  const handleSave = () => {
    logEvent("save-profile", { userId, action: "manual-save" });
    saveProfile();
  };

  return <div>...</div>;
}
```

### Structured Logging Patterns

```typescript
import {
  logUserAction,
  logBusinessEvent,
  createLogContext,
} from "~/presentation/utils/logging-utils";

// User action logging
logUserAction(logger, "update-profile", userId, {
  fieldsChanged: ["email", "phone"],
  source: "settings-page",
});

// Business event logging
logBusinessEvent(logger, "subscription-upgraded", {
  userId: "123",
  fromPlan: "basic",
  toPlan: "premium",
  revenue: 29.99,
});

// Custom structured context
const context = createLogContext("user-service", "password-reset", {
  userId: "123",
  requestSource: "forgot-password-page",
});
logger.info("Password reset initiated", context);
```

---

## Best Practices

### ✅ Do This

```typescript
// Use structured logging with context
logger.info("User action completed", {
  action: "profile-update",
  userId: "123",
  duration: 234,
});

// Set global context once
logger.setGlobalContext({ service: "auth", version: "2.0" });

// Use appropriate log levels
logger.error("Payment failed", { orderId: "456" }); // ERROR for failures
logger.info("User logged in", { userId: "123" }); // INFO for events
logger.debug("Cache lookup", { key: "user:123" }); // DEBUG for debugging

// Include relevant context
logger.warn("Slow database query", {
  query: "SELECT * FROM users",
  duration: 3500,
  threshold: 2000,
});
```

### ❌ Don't Do This

```typescript
// Unstructured logging
logger.info("User 123 updated profile in 234ms"); // Hard to parse

// Creating loggers repeatedly
function handleRequest() {
  const logger = getLogger("handler"); // ❌ Creates every request
}

// Wrong log levels
logger.error("User clicked button"); // ❌ Not an error
logger.debug("Payment processing failed"); // ❌ Should be ERROR

// Logging sensitive data
logger.info("User login", {
  password: "secret123", // ❌ Never log passwords
  creditCard: "4111111111", // ❌ Never log sensitive data
});
```

### Security Guidelines

- **❌ Never log** passwords, credit cards, API keys, tokens
- **✅ Log user IDs** but not personal information (names, emails in production)
- **✅ Use contextual IDs** (orderIds, sessionIds) for tracking
- **✅ Sanitize error messages** that might contain sensitive data

### Performance Guidelines

- **✅ Use lazy evaluation** for expensive computations in log context
- **✅ Avoid logging in tight loops** (use counters instead)
- **✅ Use DEBUG/TRACE** levels for verbose logging (filtered in production)
- **❌ Don't stringify large objects** unnecessarily

---

## Troubleshooting

### Common Issues

#### 1. Logs Not Appearing in Production

**Problem:** Logs working in development but not in Datadog

**Solutions:**

```typescript
// Check environment variables
console.log("DATADOG_CLIENT_TOKEN:", !!process.env.DATADOG_CLIENT_TOKEN);
console.log("NODE_ENV:", process.env.NODE_ENV);

// Verify initialization
initializeLogger();

// Check log levels (only FATAL, ERROR, WARN go to production)
logger.error("This will appear in production");
logger.info("This will NOT appear in production");
```

#### 2. HTTP Errors Not Being Logged

**Problem:** HTTP requests failing but no error logs

**Check:**

```typescript
// Verify you're using the logged HTTP client
import { makeHttpClient } from "~/infra/http";
const client = makeHttpClient("my-service"); // ✅ Has logging

// Not this:
import { AxiosHttpClientAdapter } from "~/infra/http/axios-http-client-adapter";
const client = new AxiosHttpClientAdapter(); // ❌ No logging
```

#### 3. Too Many Logs in Development

**Problem:** Console is cluttered with logs

**Solutions:**

```typescript
// Use appropriate log levels
logger.trace("Very detailed info"); // Most verbose
logger.debug("Debug information"); // Detailed
logger.info("Normal operations"); // Standard

// Filter in browser console
// Type "auth-service" to filter for specific service logs
```

#### 4. Missing Context in Logs

**Problem:** Logs don't have enough information

**Solution:**

```typescript
// Set global context once
logger.setGlobalContext({
  service: "user-management",
  version: "2.1.0",
  instance: "web-1",
});

// Add operation-specific context
logger.info("Operation completed", {
  operationId: "op123",
  userId: "user456",
  duration: 234,
});
```

---

## For Non-Technical Stakeholders

### What Is Our Logging System?

Think of our logging system as a **detailed diary** that our application writes about everything it does. Just like a security camera records what happens in a building, our logger records what happens in our software.

### Why Is This Important?

#### 🔍 **Problem Detection**

- **Before:** When users reported "the app is broken," we had to guess what went wrong
- **After:** We know exactly what failed, when, and why

#### 📊 **Performance Monitoring**

- Track how fast different parts of the app are running
- Identify bottlenecks before they affect users
- Monitor API response times and success rates

#### 🚨 **Proactive Issue Resolution**

- Get alerts when critical systems fail
- Fix problems before users notice them
- Understand patterns in failures

### What Information Do We Collect?

#### ✅ **We DO Log:**

- User actions (login, logout, purchases) with user IDs
- System performance (response times, error rates)
- Business events (subscriptions, payments)
- Technical errors and their causes

#### ❌ **We DON'T Log:**

- Passwords or personal information
- Credit card numbers or sensitive data
- Private user content or messages
- Anything that violates privacy

### Different Environments

#### 🛠️ **Development (Our Testing Environment)**

- **Purpose:** Help developers debug and improve the app
- **Visibility:** Very detailed logs visible to developers
- **Data:** Test data only, not real user data

#### 🚀 **Production (Live Application)**

- **Purpose:** Monitor real application health and performance
- **Visibility:** Only critical issues and business metrics
- **Data:** Real user data (but anonymized/protected)

### Business Benefits

#### 💰 **Cost Savings**

- Reduce time spent investigating issues
- Prevent revenue loss from downtime
- Optimize performance to reduce infrastructure costs

#### 😊 **Better User Experience**

- Faster issue resolution
- Proactive problem prevention
- Data-driven performance improvements

#### 📈 **Business Intelligence**

- Understand user behavior patterns
- Track feature usage and adoption
- Measure business metric trends

### Compliance and Privacy

#### 🔒 **Data Protection**

- No personal information in logs
- Automatic data retention policies
- Encrypted transmission to logging service

#### 📋 **Audit Trail**

- Complete record of system events
- Compliance with regulatory requirements
- Security incident investigation capability

### What You'll See in Reports

When we share logging insights with the business team, you'll see:

- **📊 System Health Dashboards** - Green/yellow/red status indicators
- **⏱️ Performance Metrics** - Average response times, success rates
- **🚨 Alert Summaries** - What broke, when, and how quickly we fixed it
- **📈 Trend Analysis** - How our metrics are improving over time
- **💼 Business Metrics** - User engagement, feature usage, conversion funnels

This logging system is an investment in **reliability, performance, and user satisfaction** - helping us build a better product while reducing operational overhead.

---

## Configuration Reference

### Environment Variables

```bash
# Required for production logging
DATADOG_CLIENT_TOKEN=pub_xxxxxxxxxxxxxxxx
DATADOG_SITE=datadoghq.com
DATADOG_SERVICE=my-app-name
DATADOG_ENV=production
PROJECT_VERSION=1.2.3

# Optional
DATADOG_VERSION=1.2.3
```

### Logger Functions

```typescript
// Initialize the logging system
initializeLogger(): void

// Create/get a logger instance
getLogger(serviceName?: string): AppLogger

// Clear all cached logger instances (testing only)
clearLoggerCache(): void

// Get all active loggers (debugging)
getActiveLoggers(): Map<string, AppLogger>
```

---

## Migration Guide

### From Console.log to Structured Logging

```typescript
// ❌ Old way
console.log("User logged in:", userId);
console.error("Login failed for user:", userId, error);

// ✅ New way
const logger = getLogger("auth-service");
logger.info("User logged in", { userId, sessionId });
logger.error("Login failed", {
  userId,
  error: error.message,
  reason: "invalid-credentials",
});
```

### From Manual HTTP Logging to Automatic

```typescript
// ❌ Old way - manual logging in every HTTP call
const response = await axios.get("/api/users");
console.log("API call completed:", response.status);

// ✅ New way - automatic logging with rich context
const httpClient = makeHttpClient("user-service");
const response = await httpClient.request({ method: "GET", url: "/api/users" });
// Automatically logs: timing, status, errors, performance analysis
```

---

This documentation provides everything developers need to understand and effectively use our logging system. For additional questions or examples, please refer to the implementation files or reach out to the development team.
