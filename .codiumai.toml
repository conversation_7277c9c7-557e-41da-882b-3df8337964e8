#.codiumai.toml
[tests]

## Testing framework to use - this can affect the content of the generated tests
## as well as the test run command.
## Possible values are:
##  Python: Pytest, Unittest
##  Javascript / Typescript: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, React Testing Library
##    HOWEVER: running tests in JS / TS is at the moment only supported
##    for Je<PERSON>, <PERSON>cha, Vitest, and React Testing Library
framework = "Vitest"

## An additional Javascript utility library used to test your code, if any. 
## Possible values are None, Testing Library, Enzyme, or Chai. Not applicable to Python projects.
utility_library = "Testing Library"

## A hint to the test generator about whether to use mocks or not. Possible values are true or false.
# use_mocks = false

## How many tests should be generated by default. Fewer tests is faster.
## Does not apply at the moment to extend-suite tests.
# num_desired_tests = 6

## A multiline string, delimited with triple-quotes (""") serving as an extra instruction
## that the AI model will take into consideration.
## This will appear as "General instructions" in the
## configuration section in the tests panel.
# plan_instructions = """
# Each line should have a comment explaining it.
# Each comment should start with the comment number (1., 2. etc.)
# """

## A multiline string, delimited with triple-quotes (""") serving as an example test that represents
## what you would like the generated tests to look like in terms of style, setup, etc.
# example_test = """
# describe("something", () => {
#   it("says 'bar'", () => {
#     // given
#
#     // when
#     const res = something.say();
#    
#     // Then
#     expect(res).to.equal("bar");
#   });
# });
# """


[tests.javascript]

## When running Javascript / Typescript tests, use this directory as the test process "current working directory".
## This is a path relative to the location of the config file.
## Default: The directory containing the config file.
## Note: the typical setup is to place the config file in the same directory as the relevant 'package.json' file,
## and leave this commented-out.
# overrideTestRunCwd = "./test"

## This is the command that's used to run tests.
## PLEASE READ CAREFULLY:
##
## When running tests, CodiumAI generates a temporary file that contains the test code for a single test,
## and runs that file.
## When the tests are done, the temporary file is deleted.
## For component-oriented tests (when you click "test this class" or "test this function"), the temporary file
## is created next to the file being tested.
## For extend-suite tests (when you click "add more tests" on a test-suite), the temporary file is created next
## to the test-suite file.
##
## Typically, you're going to want to take the test script defined in your package.json file, and tweak it a
## little to make it compatible with CodiumAI.
##
## You almost always want to start with 'npx' (e.g. 'npx jest', not 'npm jest' or 'yarn test').
##
## Note that the test command must be able to run test files that are located in the same directory as the
## file under test.
## A common issue is that the test command in the package.json file selects only from
## a "tests" directory, causing the CodiumAI tests be "not found" - please remove any such restriction from
## the command / configuration.
##
## The placeholder TEST_FILEPATH will be replaced with the actual test file path - this is how we find
## the file to run.
##
## EXAMPLES:
## Mocha:
##    npx ts-mocha TEST_FILEPATH --require ./test/mocha/setup.ts
## Jest:
##    npx jest --runTestsByPath TEST_FILEPATH
## 
## DEBUGGING NOTE:
## To help debug run-tests issues, you can view run logs in vscode's OUTPUT 
## (select codium-ai from the dropdown).
## It's helpful to clear the output (right-click -> clear) and then run the tests again.
##
# overrideTestRunScript = "npx jest --runTestsByPath TEST_FILEPATH"

## A multiline string, delimited with triple-quotes ("""),
## containing import declaration to use in each test file. 
# overrideImports = """ 
# import {expect} from 'chai'; """
